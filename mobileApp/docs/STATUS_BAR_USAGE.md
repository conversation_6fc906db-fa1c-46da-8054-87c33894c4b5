# Dynamic Status Bar Usage Guide

This guide explains how to use the dynamic status bar feature in the BEES Express mobile app.

## Overview

The `StatusBarService` provides a centralized way to manage status bar colors and styles throughout the app. It automatically changes the status bar based on routes and also allows manual control for specific pages.

## Features

- **Automatic route-based color changes**
- **Predefined color schemes**
- **Custom color configuration**
- **Platform-aware (only works on native platforms)**
- **Transparent status bar support**

## Basic Usage

### 1. Automatic Route-Based Changes

The status bar automatically changes based on the current route:

```typescript
// These routes automatically get different status bar colors:
/account/*          -> Yellow background (authentication)
/portal/dashboard   -> Yellow background (dashboard)
/portal/shipping    -> Blue background (shipping)
/portal/account     -> Green background (account)
/client-portal/*    -> Yellow background (client portal)
```

### 2. Manual Color Changes in Components

```typescript
import { StatusBarService } from 'src/services/status-bar.service';
import { Style } from '@capacitor/status-bar';

@Component({...})
export class YourComponent {
  constructor(private statusBarService: StatusBarService) {}

  ionViewWillEnter() {
    // Use predefined color scheme
    this.statusBarService.setColorScheme('shipping');
    
    // Or set custom colors
    this.statusBarService.setCustomStatusBar({
      backgroundColor: '#FF5722', // Orange
      style: Style.Light // Light text on dark background
    });
  }
}
```

## Available Color Schemes

### Predefined Schemes

```typescript
authentication: {
  backgroundColor: '#FFEA00', // Yellow
  style: Style.Dark
}

driverPortal: {
  backgroundColor: '#FFEA00', // Yellow
  style: Style.Dark
}

clientPortal: {
  backgroundColor: '#FFEA00', // Yellow
  style: Style.Dark
}

dashboard: {
  backgroundColor: '#FFEA00', // Yellow
  style: Style.Dark
}

shipping: {
  backgroundColor: '#2196F3', // Blue
  style: Style.Light
}

account: {
  backgroundColor: '#4CAF50', // Green
  style: Style.Light
}

default: {
  backgroundColor: '#FFEA00', // Default yellow
  style: Style.Dark
}
```

## Advanced Usage

### 1. Update Existing Color Schemes

```typescript
// Update the shipping color scheme
this.statusBarService.updateColorScheme('shipping', {
  backgroundColor: '#E91E63', // Pink
  style: Style.Light
});
```

### 2. Transparent Status Bar (Full-screen content)

```typescript
// Make status bar transparent for full-screen content
await this.statusBarService.setTransparent();

// Reset to normal mode
await this.statusBarService.resetOverlay();
```

### 3. Get Current Color Scheme

```typescript
const currentScheme = this.statusBarService.getColorScheme('dashboard');
console.log(currentScheme.backgroundColor); // '#FFEA00'
```

## Implementation Examples

### Example 1: Custom Color for Special Pages

```typescript
// In a special promotion page
ionViewWillEnter() {
  this.statusBarService.setCustomStatusBar({
    backgroundColor: '#FF6B35', // Bright orange
    style: Style.Light
  });
}

ionViewWillLeave() {
  // Reset to default when leaving
  this.statusBarService.setColorScheme('default');
}
```

### Example 2: Dynamic Color Based on Data

```typescript
ionViewWillEnter() {
  // Change color based on shipment status
  if (this.shipmentStatus === 'urgent') {
    this.statusBarService.setCustomStatusBar({
      backgroundColor: '#F44336', // Red
      style: Style.Light
    });
  } else if (this.shipmentStatus === 'completed') {
    this.statusBarService.setCustomStatusBar({
      backgroundColor: '#4CAF50', // Green
      style: Style.Light
    });
  } else {
    this.statusBarService.setColorScheme('default');
  }
}
```

### Example 3: Theme-based Colors

```typescript
// Change status bar based on user theme preference
setThemeColors(isDarkMode: boolean) {
  if (isDarkMode) {
    this.statusBarService.setCustomStatusBar({
      backgroundColor: '#121212', // Dark background
      style: Style.Light
    });
  } else {
    this.statusBarService.setCustomStatusBar({
      backgroundColor: '#FFFFFF', // Light background
      style: Style.Dark
    });
  }
}
```

## Best Practices

1. **Use predefined schemes** when possible for consistency
2. **Set status bar in `ionViewWillEnter()`** for page-specific colors
3. **Reset to default in `ionViewWillLeave()`** if using temporary colors
4. **Consider accessibility** when choosing colors and text styles
5. **Test on actual devices** as status bar appearance can vary

## Color Guidelines

- **Yellow (#FFEA00)**: Default brand color, authentication, main pages
- **Blue (#2196F3)**: Shipping, logistics, action-oriented pages
- **Green (#4CAF50)**: Success, completed actions, account pages
- **Red (#F44336)**: Urgent, errors, warnings
- **Orange (#FF5722)**: Notifications, special promotions

## Platform Notes

- Status bar changes only work on **native platforms** (iOS/Android)
- On **web/browser**, the service calls are ignored
- **iOS**: Status bar style affects text color (Dark = black text, Light = white text)
- **Android**: Status bar background color is more prominent

## Troubleshooting

1. **Status bar not changing**: Ensure you're testing on a native platform
2. **Colors not matching**: Check if the color format is correct (hex with #)
3. **Text not visible**: Adjust the style (Dark/Light) based on background color
4. **Changes not persisting**: Make sure to call the service in the right lifecycle method
