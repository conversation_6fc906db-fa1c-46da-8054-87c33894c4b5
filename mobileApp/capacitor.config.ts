import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'io.ionic.starter',
  appName: 'BEES-Express',
  webDir: 'www',
  server: {
    androidScheme: "http",
    cleartext: true,
    allowNavigation: [
      "localhost"
    ]
  },
  plugins: {
    SplashScreen: {
      launchAutoHide: false,
      showSpinner: false,
      splashFullScreen: true,
      backgroundColor: "#ffffff"
    },
    StatusBar: {
      style: "DARK",
      backgroundColor: "#FFEA00",
      overlaysWebView: false
    },
    PushNotifications: {
      presentationOptions: ["badge", "sound", "alert"],
    },
    Badge: {
      persist: true,
      autoClear: false
    },
    Keyboard: {
      resize: "ionic",
      style: "dark",
      resizeOnFullScreen: true
    },
    Camera: {
      permissions: ["camera", "photos"]
    },
    Geolocation: {
      permissions: ["location"]
    }
  }
};

export default config;
