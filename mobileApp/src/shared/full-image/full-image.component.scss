.header-section {
    //  position: fixed;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 135px;

    .header-bg {
        height: 140px;
        width: 100%;
    }
}

.image-preview-container {
    position: relative;
    width: 100%;
    // height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: white;
    margin-top: 30px;
}

.image-text {
    font-size: 14px;
    color: #BFBFBF;
}

.full-image-container {
    width: 90%;
    //  height: calc(100% - 60px);
    display: flex;
    justify-content: center;
    align-items: center;
}

.full-screen-image {
    object-fit: cover;
    border-radius: 30px;
    height: 300px;
}

.delete-icon-container {
    position: absolute;
    right: 37px;
    bottom: 125px;
    display: flex;
    justify-content: center;
    align-items: center;

    .delete-icon {
        font-size: 22px;
    }
}

ion-button {
    --background: black;
    min-height: 50px;
    min-width: 140px;
    --border-radius: 18px;
    text-transform: capitalize;
    color: white;
    font-weight: 400;
    font-size: 14px;
}