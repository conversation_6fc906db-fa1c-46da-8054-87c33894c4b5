import { Component, Input, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';

@Component({
  selector: 'app-full-image',
  templateUrl: './full-image.component.html',
  styleUrls: ['./full-image.component.scss'],
  standalone: false,
})
export class FullImageComponent implements OnInit {

  @Input() imageUrl: string | null = null;
  @Input() fileName: string | null = null;

  constructor(private modalCtrl: ModalController) { }

  ngOnInit() {

  }

  removeImage() {
    if (this.imageUrl) {
      this.modalCtrl.dismiss(this.imageUrl, 'remove'); // pass imageUrl and role 'remove'
    }
  }

  close() {
    this.modalCtrl.dismiss();
  }

}
