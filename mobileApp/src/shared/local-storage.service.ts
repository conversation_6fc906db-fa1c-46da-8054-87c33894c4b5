import { Injectable } from '@angular/core';
import { Storage } from '@ionic/storage-angular';

@Injectable({
  providedIn: 'root',
})
export class LocalStorageService {

  constructor(public storage: Storage) {
  }

  async init() {
    await this.storage.create();
  }

  // set a key/value
  set(key: string, value: any): boolean {
    try {
      localStorage.setItem(key, value);
      return true;
    } catch (reason) {
      return false;
    }
  }

  // to get a key/value pair
  get(key: string): any {
    const result = localStorage.getItem(key);
    if (result == null) {
      return null;
    }
    return result;
  }

  // set a key/value object
  setObject(key: string, object: Object): boolean {
    localStorage.setItem(key, JSON.stringify(object));
    return true;
  }

  // get a key/value object
  getObject(key: string): any {
    const result = localStorage.getItem(key);
    if (result == null) {
      return null;
    }
    return JSON.parse(result);
  }

  // remove a single key value:
  remove(key: string) {
    localStorage.removeItem(key);
  }

  clearAll() {
    localStorage.clear();
  }
}
