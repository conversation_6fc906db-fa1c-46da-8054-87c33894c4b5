import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { NavController } from '@ionic/angular';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from './auth.model';
import { EventService } from './event.service';
import { LocalStorageService } from './local-storage.service';
import { ToastService } from './toast.service';
import { AuthService } from './authservice';

@Injectable({
  providedIn: 'root'
})
export class FcmService {

  constructor(private router: Router,
    private toastService: ToastService,
    private readonly loadingService: LoadingService,
    private dataService: DataService,
    private readonly navController: NavController,
    private eventService: EventService,
    private localStorageService: LocalStorageService,
    private authService: AuthService) { }

  
}
