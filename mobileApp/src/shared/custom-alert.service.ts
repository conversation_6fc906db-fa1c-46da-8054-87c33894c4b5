import { Injectable } from '@angular/core';
import { AlertController } from '@ionic/angular';

export interface CustomAlertOptions {
  header: string;
  message: string;
  icon?: string;
  confirmText?: string;
  cancelText?: string;
  confirmHandler?: () => void;
  cancelHandler?: () => void;
}

@Injectable({
  providedIn: 'root'
})
export class CustomAlertService {

  constructor(private alertController: AlertController) {}

  async presentAlert(options: CustomAlertOptions): Promise<void> {
    const alert = await this.alertController.create({
      header: options.header,
      message: options.message,
      buttons: [
        {
          text: options.confirmText || 'Logout',
          cssClass: 'logout-confirm-button',
          handler: () => {
            if (options.confirmHandler) {
              options.confirmHandler();
            }
          }
        },
        {
          text: options.cancelText || 'Cancel',
          role: 'cancel',
          cssClass: 'logout-cancel-button'
        }
      ],
      cssClass: 'custom-logout-alert',
      backdropDismiss: false
    });

    await alert.present();
  }
}
