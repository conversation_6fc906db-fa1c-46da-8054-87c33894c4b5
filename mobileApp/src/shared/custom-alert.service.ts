import { Injectable } from '@angular/core';
import { ActionSheetController } from '@ionic/angular';

export interface CustomAlertOptions {
  header: string;
  message: string;
  icon?: string;
  confirmText?: string;
  cancelText?: string;
  confirmHandler?: () => void;
  cancelHandler?: () => void;
}

@Injectable({
  providedIn: 'root'
})
export class CustomAlertService {

  constructor(private actionSheetController: ActionSheetController) {}

  async presentAlert(options: CustomAlertOptions): Promise<void> {
    const actionSheet = await this.actionSheetController.create({
      header: options.header,
      subHeader: options.message,
      cssClass: 'custom-logout-action-sheet',
      buttons: [
        {
          text: options.confirmText || 'Logout',
          role: 'destructive',
          cssClass: 'logout-button',
          handler: () => {
            if (options.confirmHandler) {
              options.confirmHandler();
            }
          }
        },
        {
          text: options.cancelText || 'Cancel',
          role: 'cancel',
          cssClass: 'cancel-button'
        }
      ]
    });

    await actionSheet.present();
  }
}
