import { Injectable } from '@angular/core';
import { AlertController } from '@ionic/angular';

export interface CustomAlertOptions {
  header: string;
  message: string;
  icon?: string;
  confirmText?: string;
  cancelText?: string;
  confirmHandler?: () => void;
  cancelHandler?: () => void;
}

@Injectable({
  providedIn: 'root'
})
export class CustomAlertService {

  constructor(private alertController: AlertController) {}

  async presentAlert(options: CustomAlertOptions): Promise<void> {
    const alert = await this.alertController.create({
      header: options.header,
      message: options.message,
      buttons: [
        {
          text: options.cancelText || 'Cancel',
          role: 'cancel',
          cssClass: 'alert-cancel-button',
          handler: () => {
            if (options.cancelHandler) {
              options.cancelHandler();
            }
          }
        },
        {
          text: options.confirmText || 'Confirm',
          cssClass: 'alert-confirm-button',
          handler: () => {
            if (options.confirmHandler) {
              options.confirmHandler();
            }
          }
        }
      ],
      cssClass: 'custom-styled-alert',
      backdropDismiss: false
    });

    await alert.present();
  }
}
