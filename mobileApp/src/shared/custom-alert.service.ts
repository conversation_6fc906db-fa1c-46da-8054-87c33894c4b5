import { Injectable } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { CustomLogoutModalComponent } from './custom-logout-modal.component';

export interface CustomAlertOptions {
  header: string;
  message: string;
  icon?: string;
  confirmText?: string;
  cancelText?: string;
  confirmHandler?: () => void;
  cancelHandler?: () => void;
}

@Injectable({
  providedIn: 'root'
})
export class CustomAlertService {

  constructor(private modalController: ModalController) {}

  async presentAlert(options: CustomAlertOptions): Promise<void> {
    const modal = await this.modalController.create({
      component: CustomLogoutModalComponent,
      componentProps: {
        header: options.header,
        message: options.message,
        confirmText: options.confirmText || 'Logout',
        cancelText: options.cancelText || 'Cancel',
        confirmHandler: options.confirmHandler,
        cancelHandler: options.cancelHandler
      },
      cssClass: 'custom-logout-modal',
      backdropDismiss: false,
      showBackdrop: true
    });

    await modal.present();
  }
}
