import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, RouterStateSnapshot } from '@angular/router';
import { NavController } from '@ionic/angular';
import { AuthService } from './authservice';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {

  constructor(private readonly authService: AuthService,
    private readonly navController: NavController,
  ) {
  }

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    const roles = route.data['roles'] as Array<string>;
    const response: any = this.authService.isAuthorizedUser(roles);
    if (roles.indexOf('ROLE_ANONYMOUS') !== -1) {
      if (response?.hasAccess) {
        const user = this.authService.getUser();

        // Temporary fix: Always redirect to driver dashboard
        // TODO: Remove this after fixing the role detection issue
        if (this.authService.isCustomer() && !this.authService.isDriver()) {
          this.navController.navigateForward("/client-portal/dashboard");
          return false;
        }
        this.navController.navigateForward("/portal/dashboard");
        return false;
      }
      return true;
    }
    if (!response.hasAccess || !response.hasRoleAccess) {
      this.authService.logout();
      this.navController.navigateRoot("/account/login", { queryParams: { redirectTo: state.url }, animated: true });
      return false;
    }
    if (!response.hasRoleAccess) {
      this.navController.navigateRoot("403");
      return false;
    }
    return true;
  }

}


