import { Component, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { ImageValidateService } from 'src/services/image-validate.service';

declare const $: any;

@Component({
  selector: 'app-file-cropper',
  templateUrl: './file-cropper.component.html',
  styleUrls: ['./file-cropper.component.scss'],
  standalone: false,
})
export class FileCropperComponent implements OnInit {

  request: any;
  isOpenCrop: boolean;
  file: any;
  constructor(
    private imageValidateService: ImageValidateService,
    private modalCtrl: ModalController) {
    this.request = {} as any;
    this.isOpenCrop = false;
  }

  ngOnInit() {
    this.init();
  }

  async init() {
    this.request = {} as any;
    this.request.imageBase64 = this.file.base64String;
    this.isOpenCrop = true;
  }

  upload() {
    this.isOpenCrop = false;
    const imageData = this.request.croppedImage;
    this.modalCtrl.dismiss({ croppedFile: imageData }, 'confirm'); // Update to pass croppedFile
  }

  imageCropped(event: any) {
    this.request.croppedImage = event.blob; // Ensure this is correct
  }  

  async close() {
    this.isOpenCrop = false;
    this.modalCtrl.dismiss(null, 'cancel');
  }
}
