body::-webkit-scrollbar {
    display: none;
}

.margin-top-2 {
    margin-top: 2px !important;
}

.margin-top-3 {
    margin-top: 3px;
}

.margin-top-4 {
    margin-top: 4px !important;
}

.margin-top-5 {
    margin-top: 5px !important;
}

.margin-top-6 {
    margin-top: 6px !important;
}

.margin-top-7 {
    margin-top: 7px !important;
}

.margin-top-8 {
    margin-top: 8px !important;
}

.margin-top-10 {
    margin-top: 10px !important;
}

.margin-top-12 {
    margin-top: 12px !important;
}

.margin-top-15 {
    margin-top: 15px !important;
}

.margin-top-20 {
    margin-top: 20px !important;
}

.margin-top-25 {
    margin-top: 25px !important;
}

.margin-top-30 {
    margin-top: 30px !important;
}

.margin-top-35 {
    margin-top: 35px !important;
}

.margin-top-40 {
    margin-top: 40px !important;
}

.margin-top-45 {
    margin-top: 45px !important;
}

.margin-top-50 {
    margin-top: 50px !important;
}

.margin-top-55 {
    margin-top: 55px !important;
}

.margin-top-60 {
    margin-top: 60px !important;
}

.margin-top-70 {
    margin-top: 70px !important;
}

.margin-top-80 {
    margin-top: 80px !important;
}

.margin-top-110 {
    margin-top: 110px !important;
}

.margin-top-15vw {
    margin-top: 15vw !important;
}

/*------------*/

.margin-bottom-5 {
    margin-bottom: 5px !important;
}

.margin-bottom-8 {
    margin-bottom: 8px !important;
}

.margin-bottom-10 {
    margin-bottom: 10px !important;
}

.margin-bottom-15 {
    margin-bottom: 15px !important;
}

.margin-bottom-20 {
    margin-bottom: 20px !important;
}

.margin-bottom-25 {
    margin-bottom: 25px !important;
}

.margin-bottom-30 {
    margin-bottom: 30px !important;
}

.margin-bottom-35 {
    margin-bottom: 35px !important;
}

.margin-bottom-40 {
    margin-bottom: 40px !important;
}

.margin-bottom-45 {
    margin-bottom: 45px !important;
}

.margin-bottom-50 {
    margin-bottom: 50px !important;
}

.margin-bottom-55 {
    margin-bottom: 55px !important;
}

.margin-bottom-60 {
    margin-bottom: 60px !important;
}

.margin-bottom-70 {
    margin-bottom: 70px !important;
}

.margin-bottom-80 {
    margin-bottom: 80px !important;
}

/*--------------*/

.margin-left-5 {
    margin-left: 5px !important;
}

.margin-left-7 {
    margin-left: 7px !important;
}

.margin-left-8 {
    margin-left: 8px !important;
}

.margin-left-10 {
    margin-left: 10px !important;
}

.margin-left-12 {
    margin-left: 12px !important;
}

.margin-left-15 {
    margin-left: 15px !important;
}

.margin-left-20 {
    margin-left: 20px !important;
}

.margin-left-25 {
    margin-left: 25px !important;
}

.margin-left-30 {
    margin-left: 30px !important;
}

.margin-left-35 {
    margin-left: 35px !important;
}

.margin-left-40 {
    margin-left: 40px !important;
}

.margin-left-45 {
    margin-left: 45px !important;
}

.margin-left-50 {
    margin-left: 50px !important;
}

.margin-left-55 {
    margin-left: 55px !important;
}

.margin-left-60 {
    margin-left: 60px !important;
}

/*--------------*/

.margin-right-5 {
    margin-right: 5px !important;
}

.margin-right-7 {
    margin-right: 7px !important;
}

.margin-right-10 {
    margin-right: 10px !important;
}

.margin-right-12 {
    margin-right: 12px !important;
}

.margin-right-15 {
    margin-right: 15px !important;
}

.margin-right-20 {
    margin-right: 20px !important;
}

.margin-right-25 {
    margin-right: 25px !important;
}

.margin-right-30 {
    margin-right: 30px !important;
}

.margin-right-35 {
    margin-right: 35px !important;
}

.margin-right-40 {
    margin-right: 40px !important;
}

.margin-right-45 {
    margin-right: 45px !important;
}

.margin-right-50 {
    margin-right: 50px !important;
}

.margin-right-55 {
    margin-right: 55px !important;
}

.margin-right-60 {
    margin-right: 60px !important;
}

/*------------*/

.no-margin-left {
    margin-left: 0px !important;
}

.no-margin-bottom {
    margin-bottom: 0px !important;
}

.no-margin-top {
    margin-top: 0px !important;
}

.no-margin-right {
    margin-right: 0px !important;
}

.no-margin {
    margin: 0px !important;
}

/*End Margin Classes*/

/*Padding Classes*/

.no-padding {
    padding: 0px !important;
}

.no-padding-left {
    padding-left: 0px !important;
}

.no-padding-right {
    padding-right: 0px !important;
}

.no-padding-top {
    padding-top: 0px !important;
}

.no-padding-bottom {
    padding-bottom: 0px !important;
}

.padding-5 {
    padding: 5px;
}

.padding-10 {
    padding: 10px;
}

.padding-15 {
    padding: 15px;
}

.padding-20 {
    padding: 20px !important;
}

.padding-left-5 {
    padding-left: 5px;
}

.padding-left-10 {
    padding-left: 10px !important;
}

.padding-left-15 {
    padding-left: 15px;
}

.padding-left-20 {
    padding-left: 20px;
}

.padding-right-5 {
    padding-right: 5px;
}

.padding-right-10 {
    padding-right: 10px !important;
}

.padding-right-15 {
    padding-right: 15px;
}

.padding-right-20 {
    padding-right: 20px;
}

.padding-top-3 {
    padding-top: 3px;
}

.padding-top-5 {
    padding-top: 5px;
}

.padding-top-10 {
    padding-top: 10px !important;
}

.padding-top-15 {
    padding-top: 15px !important;
}

.padding-top-20 {
    padding-top: 20px !important;
}

.padding-bottom-3 {
    padding-bottom: 3px;
}


.padding-bottom-5 {
    padding-bottom: 5px;
}

.padding-bottom-10 {
    padding-bottom: 10px !important;
}

.padding-bottom-15 {
    padding-bottom: 15px;
}

.padding-bottom-20 {
    padding-bottom: 20px;
}

.disable {
    display: none;
}

.no-border-bottom {
    border-bottom: 0px !important;
}

// customize width
.full-width {
    width: 100%;
}

.site-full-rounded-button {
    min-height: 54px;
    border-radius: 100px;

    &.less-border-radius {
        --border-radius: 12px;
        font-size: 13px;
        --background-activated: #29385b;
    }

    &.text-capitalize {
        text-transform: uppercase;
    }

    &.one-third-width {
        max-width: calc(100vw - 135px);
        width: 100%;
    }

    &.min-width-120 {
        min-width: 120px;
    }

    &.min-width-150 {
        min-width: 150px;
    }

    &.remaining-width {
        max-width: calc(100vw - 171px);
        width: 100%;
    }

    &.small-button {
        min-height: 38px;
    }

    &.full-width {
        width: 100%;
    }

    &.close-button-hotel {
        --background: #999 !important;
    }

    &.primary-button {
        margin-top: 15px;
        --background: #29385b !important;
    }

    &.green-button {
        --background: #07944f !important;
        --background-activated: #07944f !important;
        --background-focused: #07944f !important;
        --background-hover: #07944f !important;
    }

    &.default-button {
        --background: #e8e8e8 !important;
        --background-activated: #e8e8e8 !important;
        --background-focused: #e8e8e8 !important;
        --background-hover: #e8e8e8 !important;
        --color: #0d0d0d !important;
    }

    &.danger-button {
        --background: #fa4856 !important;
        --background-activated: #fa4856 !important;
        --background-focused: #fa4856 !important;
        --background-hover: #fa4856 !important;
    }

    &.button-disabled {
        --background: #cfd2d3 !important;
        --color: #858c8f;
    }

    &.medium-text {
        font-size: 17px;
        font-weight: 500;
    }
}

.site-less-rounded-button {
    min-height: 54px;
    --border-radius: 8px !important;
    width: 100%;

    &.primary {
        --background: #0d0d0d !important;
    }

    &.button-disabled {
        --background: #cfd2d3 !important;
        --color: #858c8f;
    }
}

.custom-modal-notifications {
    text-align: center;
    margin: 10px 0px 10px 0px;
    font-size: 17px;
}

.document-upload-container {
    border: 2px dotted black;
    border-radius: 20px;
    padding: 22px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    cursor: pointer;
    background-color: white;
    transition: background-color 0.3s ease;
    margin: 10px 5px;
    position: relative;
}

.upload-text {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.upload-icon {
    width: 40px;
    height: 40px;
    margin-bottom: 10px;
}

.choose-file-text {
    font-size: 14px;
    font-weight: bold;
    color: black;
}

.upload-multiple-file-text {
    font-size: 10px;
    color: #BFBFBF;
}

// New Gallery Grid
.image-grid {
    display: grid;
    gap: 7px;
    padding-left: 5px;
    grid-template-columns: repeat(3, 1fr);

    .image-item {
        position: relative;
        width: 100px;
        height: 100px;
        border-radius: 8px;
        overflow: hidden;
        background-color: #f2f2f2;
        cursor: pointer;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: filter 0.3s;
        }

        &.selected img {
            filter: brightness(30%) blur(1px);
        }

        .icon-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2;
            gap: 7px;

            .icon {
                font-size: 18px;
            }
        }
    }
}

.site-form-control {
    --border-width: 1px #D0D0D0 !important;
    --border-radius: 22px;
    --inner-padding-start: 0px;
    --inner-padding-end: 0px;
    --padding-start: 16px;
    --padding-end: 16px;

    &.search-form-control {
        --border-width: 0px !important;
    }

    &.is-invalid {
        --border-color: #dc3545 !important;
    }

    &.special-request-disabled {
        opacity: 1;
        width: 150px;
        --border-width: 0px !important;
        --inner-padding-start: 0px;
        --inner-padding-end: 0px;
        --padding-start: 0px;
        --padding-end: 0px;
    }

    ion-input {
        font-size: 14px;
        --padding-start: 0px !important;
        --padding-end: 0px;
        //  --padding-top: 3px;
        --padding-bottom: 0px;
        font-weight: 500;
        min-height: 50px !important;

        &.special-request-width {
            max-width: 155px;
            min-height: 0px !important;
        }

        &.search-ion-input {
            --padding-bottom: 15px;
            min-height: 40px !important;
            color: black;
            text-align: center;

            // Center align the input text
            input {
                text-align: center !important;
            }

            // Center align the label when floating
            ion-label {
                text-align: center;
            }
        }

        &.has-start-icon {
            --padding-start: 45px !important;
        }

        &.no-font-weight {
            font-weight: 400;
        }

        &.special-request-disabled {
            pointer-events: none;
            opacity: 1;
            background-color: #F3F3F1;
            min-height: 50px !important;
        }
    }

    ion-select {
        font-size: 14px;
        --padding-start: 0px !important;
        --padding-end: 0px;
        --padding-top: 3px;
        --padding-bottom: 0px;
        font-weight: 500;
        min-height: 50px !important;

        &::part(icon) {
            display: none;
        }

        &::part(container) {
            border: none;
        }

        &::part(text) {
            color: #333;
            font-weight: 500;
        }

        &::part(placeholder) {
            color: #999;
            opacity: 0.8;
        }

        &[disabled] {
            color: black;
            opacity: 1;
            pointer-events: none;
        }
    }

    ion-input[disabled] {
        color: black;
        opacity: 1;
        pointer-events: none;
    }

    .start-icon {
        margin-right: 13px;
        color: black;
        width: 18px;
        height: 18px;
    }

    &.mobile-form-control {
        --padding-top: 6px;
        --padding-bottom: 6px;
    }
}

.common-fields-container {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.field-container {
    flex: 1;
    //   min-width: 0;
    margin-top: 10px;
}

.small-field {
    flex: 1;
    //  max-width: 100px;
}

.large-field {
    flex: 1;
}

.site-form-floating-control {
    border-radius: 6px;
    --inner-padding-start: 16px;
    --inner-padding-end: 16px;
    --inner-padding-top: 2px;
    --inner-padding-bottom: 0px;
    --padding-start: 0px;
    --padding-bottom: 0px;
    border: 2px solid var(--ion-color-input-border);
    --min-height: 60px !important;
    height: 65px;

    &.child-section {
        margin-top: 10px;
    }

    &.padding-start {
        // --inner-padding-top: 2px;
        --padding-start: 16px;
        --inner-padding-start: 0px;
        --inner-padding-end: 0px;
        height: 60px;
    }

    &::part(native) {
        border-radius: 12px !important;
    }

    ion-input {
        border-radius: 12px;
        font-size: 15px;
    }

    ion-textarea {
        border-radius: 12px;
        font-size: 15px;
    }

    &.large-font {
        ion-input {
            border-radius: 12px;
            font-size: 17px;
        }
    }

    ion-label {
        margin-top: 0px !important;
    }

    .label-floating {
        transform: translate(0, 19px);
    }

    &.is-invalid {
        border: 2px solid var(--ion-color-warning-dark-red) !important;
    }

    .caret-down-icon {
        position: absolute;
        right: 5px;
        top: 50%;
        margin-top: -13.5px;
        cursor: pointer;
        z-index: 9999;
    }

    .value-display-label {
        margin-bottom: 0px !important;
        font-size: 15px;
        padding: 12px 0px;
        padding-right: 10px;
        max-width: calc(100% - 20px);
    }

    .slot-start {
        margin-inline: 0px;
        margin-top: 19px;
        margin-left: 15px;
        margin-bottom: 19px;
        font-size: 22px;
    }

    .custom-select-float-placeholder {
        font-size: 17px;
        margin-bottom: 0px !important;
        padding: 12px 0px;
        padding-right: 10px;
        max-width: calc(100% - 20px);
    }

    .start-icon {
        margin-top: 20px;
    }
}

.fixed-search {
    position: fixed;
    top: 69px;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 10px 0;
    border-top: 1px solid #ddd;

    &.home-page-tracking {
        background-color: #FFEA00;
        border-top: unset;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        gap: 10px;
        position: unset;
        box-shadow: unset;
        padding: 0px;
    }

    &.shipment-page-search {
        display: flex;
        align-items: center;
        justify-content: space-between;
        top: 160px;
        background-color: unset;
        box-shadow: unset;
        padding: 10px 20px;
        border-top: unset;
        gap: 5px;
        position: unset;
    }

    &.quotation-page-search {
        display: flex;
        align-items: center;
        justify-content: space-between;
        top: 160px;
        background-color: unset;
        box-shadow: unset;
        padding: 10px 20px;
        border-top: unset;
        gap: 5px;
        position: unset;
    }

    .padding-top {
        padding-top: 0;
        width: 100%;
    }
}

.search-container ion-item {
    width: 100%;
}

.site-input-group {
    padding: 0px 16px;
    border-radius: 12px;
    border: 2px solid var(--ion-color-input-border);

    .site-form-floating-control {
        border-radius: 0px !important;
        border: 0px !important;
        --inner-padding-start: 0px;
        --inner-padding-end: 0px;
        --inner-padding-top: 7px;
        --inner-padding-bottom: 7px;
        border-bottom: 1px solid var(--ion-color-input-border) !important;

        &:last-child {
            border: 0px !important;
        }

        .edit-input-label {
            margin-top: 18px;
        }
    }
}

.custom-form-control {
    position: relative;
    font-size: 17px;
    border: 2px solid var(--ion-color-input-border);
    border-radius: 12px;
    padding: 19px 16px;
    background-color: var(--ion-color-primary-contrast);
    display: flex;
    justify-content: center;
    flex-direction: column;

    .caret-down-icon {
        position: absolute;
        right: 15px;
        top: 15px;
        cursor: pointer;
        z-index: 9999;
        font-size: 27.2px;
    }

    &.has-content {
        font-size: 13.94px !important;
        padding: 8px 16px !important;
    }

    &.is-invalid {
        border: 2px solid var(--ion-color-warning-dark-red) !important;
    }

    .custom-value-display-value {
        padding-top: 8px;
        font-size: 15px;
    }
}

.error-message {
    text-align: left;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 12px;
    color: var(--ion-color-warning-dark-red);
    font-weight: 600;
}

.item-has-focus.label-floating.sc-ion-label-ios-h,
.item-has-focus .label-floating.sc-ion-label-ios-h,
.item-has-placeholder.sc-ion-label-ios-h:not(.item-input).label-floating,
.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-ios-h,
.item-has-value.label-floating.sc-ion-label-ios-h,
.item-has-value .label-floating.sc-ion-label-ios-h {
    transform: translate(0, 8px) scale(0.82);
}

ion-modal {
    --box-shadow: 0 28px 48px rgba(0, 0, 0, 0.4) !important;
    --backdrop-opacity: var(--ion-backdrop-opacity, 0.05) !important;
}

.green-text {
    color: var(--ion-color-green-text) !important;
}

.secondary-text {
    color: var(--ion-color-secondary-text) !important;
}

.grey-text {
    color: var(--ion-color-custom-note) !important;
}

.red-text {
    color: var(--ion-color-red) !important;
}

// end above css for customer layout tabs design according to adobe design //
.driver-bees-navigation {
    ion-tab-bar {
        height: 70px;
        border: none;
        --background: white !important;
        border-top-left-radius: 30px;
        border-top-right-radius: 30px;
        box-shadow: 0 5px 7px rgba(0, 0, 0, 0.1);

        ion-tab-button {
            height: 100%;
            --ripple-color: transparent;

            .icon-container-bees {
                //  width: 40px;
                //  height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.4rem;
                background: var(--ion-color-primary-contrast);
                border-radius: 50%;

                .non-selected-icon {
                    display: block;
                }

                .selected-icon {
                    display: none !important;
                }
            }

            ion-label {
                font-size: 12px;
                font-weight: 500;
                color: #BFBFBF;
                margin-top: 5px;
                margin-bottom: 0px;

                @media screen and (max-width: 375px) {
                    font-size: 10px !important;
                }
            }

            ion-label.active-label {
                color: black !important;
            }

            &.tab-selected {
                .icon-container-bees {
                    //  background-color: var(--ion-color-primary);
                    font-size: 1.4rem;

                    .non-selected-icon {
                        display: none !important;
                    }

                    .selected-icon {
                        display: block !important;
                        color: white;
                    }
                }

                ion-label {
                    color: black;
                }
            }
        }
    }

    &.hide-tab-bar {
        ion-tab-bar.tabs {
            display: none !important;
        }
    }
}

.logout-card {
    margin: 16px;
    background: var(--ion-color-primary);
    border-radius: 10px;

    .logout-text {
        text-align: center;
        color: var(--ion-color-primary-contrast);
        font-weight: bold;
        cursor: pointer;
        font-size: 17px;
    }
}

// end side-menu css code //

.white-background {
    background-color: var(--ion-color-primary-contrast);
}

.custom-note {
    font-size: 13px;
    color: var(--ion-color-custom-note) !important;
}

.custom-loading-container {
    img {
        border-radius: 50%;
    }
}

.shippment-btn-container {
    display: flex;
    justify-content: space-between;
}

.fuel-receipt-btn-container {
    display: flex;
    justify-content: space-between;
}

.site-button {
    //   margin-bottom: 16px;
    font-size: 12px;
    --background: black;
    min-height: 54px;
    --box-shadow: 0px 3px 6px var(--ion-color-box-shadow);
    --border-radius: 14px;
    text-transform: capitalize;
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    // Interactive effects
    &:hover:not([disabled]) {
        transform: translateY(-1px);
        --box-shadow: 0px 6px 12px var(--ion-color-box-shadow);
    }

    &:active:not([disabled]) {
        transform: translateY(0);
        transition: all 0.1s ease;
    }

    // Ripple effect
    ion-ripple-effect {
        color: rgba(255, 255, 255, 0.4);
    }

    // &:last-child {
    //     margin-bottom: 0px !important;
    // }

    &.ship-cancel-btn {
        min-width: 150px;
        min-height: 48px;
        border: 2px solid #BFBFBF;
        --background: white;
        border-radius: 14px;
        color: #BFBFBF;
        font-size: 14px;
        font-weight: 700;

        &:hover:not([disabled]) {
            border-color: #999;
            color: #999;
            transform: translateY(-1px);
        }

        ion-ripple-effect {
            color: rgba(0, 0, 0, 0.1);
        }
    }

    &.ship-submit-btn {
        min-width: 150px;
        min-height: 48px;
        font-size: 14px;
        font-weight: 600;

        &:hover:not([disabled]) {
            --background: #333;
        }
    }
}

.site-custom-popup {
    --height: auto !important;
    align-items: flex-end;
    --box-shadow: none !important;
    --backdrop-opacity: var(--ion-backdrop-opacity, 0.32) !important;
    --max-height: 98vh;

    .site-custom-popup-container {
        height: 100%;

        &.min-height {
            min-height: 250px;
        }
    }

    .site-custom-popup-header {
        text-align: center;
        position: relative;
        padding: 16px;

        &.skip-header {
            text-align: end;
        }

        &.heading-setting {
            display: flex;
            justify-content: center;

        }

        .skip-text {
            font-size: 17px;
            font-weight: bold;
            text-decoration: underline;
        }

        i-feather {
            position: absolute;
            top: 16px;
            left: 16px;
        }

        .header-text {
            text-rendering: auto;
            color: #0b1920;
            font-size: 17px;
            font-weight: 600;
        }

        &.no-header-text {
            padding: 28px;
        }

        &.less-padding {
            padding: 25px;
        }

        &.book-now-padding {
            padding: 20px;
        }
    }

    .site-custom-popup-body {
        max-height: calc(100vh - 73px);
        position: relative;
        background-color: #fff;
        overflow: auto;

        &.full-screen {
            height: 100vh !important;
        }

        &.scroll-able {
            overflow-x: scroll;
        }

        .popup-large-heading {
            font-weight: 700;
            font-size: 28px;
            margin-bottom: 6px;
            font-family: "Oswald", sans-serif;
            text-align: center;

            &.text-center {
                text-align: center;
            }

            &.book-now-heading {
                font-size: 25px;
            }
        }

        .popup-medium-heading {
            font-size: 16px;
        }

        .book-now-medium-heading {
            font-size: 17px;
            color: black;
            font-weight: 400;
            text-align: left;

            .ios & {
                font-size: 18px;
            }
        }

        .popup-expire-holiday-heading {
            font-size: 18px;
        }

        .popup-normal-heading {
            font-size: 14px;
            text-align: center;

            &.bold-text {
                font-weight: 500;
            }
        }

        .popup-text-container {
            min-height: 120px;

            &.medium {
                min-height: 95px;
            }

            &.small {
                min-height: 70px;
            }

            &.auto {
                min-height: auto !important;
            }
        }

        .resolution-con {
            .title {
                font-size: 18px;
                font-weight: 600;
                margin: 5px 0;
                font-family: "Oswald", sans-serif;
            }

            .desc {
                font-size: 12px;
                margin-bottom: 20px;
            }

            .image-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                /* 3 images per row */
                gap: 10px;
                /* Space between images */
                padding: 10px;
            }

            .image-item {
                display: flex;
                height: 100%;
                align-items: center;
            }

            .image-item img {
                width: 100%;
                height: auto;
                border-radius: 8px;
                /* Optional: Add rounded corners */
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                /* Optional: Add a shadow effect */
            }

        }

    }

    &.job-invitation-popup {
        .site-custom-popup-body {
            height: calc(100% - 68px);

            &.random-campaign-border-radius {
                border-radius: 12px;
            }

            .img-cont {
                display: flex;
                justify-content: left;
            }

            img {
                margin: 20px 0;
                height: 100px;
                border-radius: 8px;
            }
        }
    }

    &.adults-selection-popup {}

    &.filter-popup {
        .site-custom-popup-body {
            background-color: #f7f7f7;
            overflow: auto;
            height: calc(100vh - 165px);
        }

        &.ios {
            .site-custom-popup-body {
                height: calc(100vh - (165px + var(--ion-safe-area-top))) !important;
            }
        }
    }

    .site-custom-popup-footer {
        padding: 16px;
    }

    &.full-screen-popup {
        --max-height: 100vh !important;

        .site-custom-popup-container {
            height: 100vh;
        }
    }
}

ion-spinner {
    &.dots {
        height: 50px;
        width: 50px;
    }
}

.custom-mobile-input {
    border: 0px !important;
    outline: none !important;
    height: 40px !important;
    font-size: 14px;
    font-weight: 500;
    color: rgb(11, 25, 32);
    background-color: transparent !important;

    &::placeholder {
        color: rgb(11, 25, 32);
        opacity: 1;
    }

    &::-ms-input-placeholder {
        color: rgb(11, 25, 32);
        opacity: 1;
    }
}

.swiper-pagination {
    position: absolute;
    justify-content: center;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 10;
    width: 114px !important;
}

.swiper-pagination-bullet {
    width: 12px;
    height: 12px;
    background-color: #ccc;
    border-radius: 50%;
    transition: background-color 0.3s;
}

.swiper-pagination-bullet-active {
    background-color: #29385B;
}

.swiper-pagination-bullet:hover {
    background-color: #B0B8C1;
}