.onboarding-page {
    z-index: 999999999;
    // Remove top padding/margin for iOS safe area
    --padding-top: 0;
    --offset-top: 0;

    .custom-form {
        height: 100%;
    }

    .onboarding-header-section {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        // Extend to top of screen including safe area
        padding-top: env(safe-area-inset-top, 0px);
        margin-top: calc(-1 * env(safe-area-inset-top, 0px));

        .back-button {
            position: absolute;
            top: 40px;
            left: 10px;

            ion-icon {
                font-size: 30px;
                color: #000; // You can adjust this color
            }
        }

        .bees-logo-image {
            position: absolute;
            top: 35%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }

    .onboarding-page-container {
        z-index: 999999999;
        padding: 0px 20px 0px;
        text-align: center;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        background-color: var(--ion-color-primary-contrast);
        min-height: 0%;

        .onboarding-container {
            display: flex;
            flex-direction: column;
            gap: 5px;

            .page-heading {
                margin-top: 15px;
                font-size: 13px;
                font-weight: bold;
            }

            .page-heading-title {
                font-size: 14px;
            }
        }

        .onboarding-progress {
            width: 100%;
            background-color: #e6e6e6af;
            border-radius: 100px;
            height: 8px;
            margin-top: 20px;
            margin-bottom: 12px;

            .onboarding-progress-completed {
                background-color: yellow;
                border-radius: 100px;
                height: 8px;
            }
        }

        .lock-container {
            display: flex;
            align-items: center;
            justify-content: center;

            .lock-icon {
                font-size: 40px;
                border: 1px solid #D4D4D4;
                border-radius: 18px;
                padding: 12px;
            }
        }

        .profile-picture-wrapper {
            position: relative;
            max-width: 150px;
            margin: 0px auto;
            display: flex;
            justify-content: center;
            align-items: center;

            .profile-picture-container {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100px;
                height: 100px;
                border-radius: 50%;
                overflow: hidden;
                background-color: #f0f0f0;
                //  padding: 8px;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    border-radius: 50%;
                }
            }

            .action-icon {
                background-color: #29385b;
                border-radius: 50%;
                font-size: 18px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                position: absolute;
                z-index: 1;
                padding: 12px;
                bottom: 0px;
                right: 13px;

                img {
                    width: 16px;
                    height: 16px;
                }
            }
        }

        .form-container {
            min-height: 250px;
        }

        .register-container {
            display: flex;
            justify-content: center;
            align-items: center;

            p {
                font-size: 14px;
                color: var(--ion-color-primary);
                font-weight: 600;
                display: flex;
                gap: 5px;
            }

            a {
                color: #250ED0;
                font-weight: 600;
                text-decoration: underline;
            }
        }

        .privacy-container {
            color: black;
            font-size: 13px;
            text-align: center;

            a {
                color: black;
                font-weight: 600;
            }
        }

    }
}