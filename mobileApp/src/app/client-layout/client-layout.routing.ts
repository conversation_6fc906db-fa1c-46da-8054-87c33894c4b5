import { Routes } from "@angular/router";
import { ClientLayoutComponent } from "./client-layout.component";
import { ClientHomeComponent } from "./client-home/client-home.component";
import { ClientShippingComponent } from "./client-shipping/client-shipping.component";
import { ClientQuotationComponent } from "./client-quotation/client-quotation.component";
import { ClientQuotationBasicInfoComponent } from "./client-quotation-basic-info/client-quotation-basic-info.component";
import { ClientCalendarComponent } from "./client-calendar/client-calendar.component";
import { ClientAccountComponent } from "./client-account/client-account.component";
// Import client shipping sub-components (commented out until created)
// import { ClientShippingBasicInfoComponent } from "./client-shipping-basic-info/client-shipping-basic-info.component";
// import { ClientShippingBasicInfo2Component } from "./client-shipping-basic-info-2/client-shipping-basic-info-2.component";
// import { ClientShippingPickupAddressComponent } from "./client-shipping-pickup-address/client-shipping-pickup-address.component";
// import { ClientShippingDeliveryAddressComponent } from "./client-shipping-delivery-address/client-shipping-delivery-address.component";

export const CLIENTLAYOUTROUTING: Routes = [
    {
        path: "",
        component: ClientLayoutComponent,
        children: [
            {
                path: "dashboard",
                component: ClientHomeComponent
            },
            {
                path: "shipping",
                component: ClientShippingComponent
            },
            {
                path: "quotation",
                component: ClientQuotationComponent
            },
            {
                path: "quotation/basic-info",
                component: ClientQuotationBasicInfoComponent
            },
            {
                path: "calendar",
                component: ClientCalendarComponent
            },
            {
                path: "account",
                component: ClientAccountComponent
            },
            // Client shipping routes (commented out until components are created)
            // {
            //     path: 'shipping/basic-info',
            //     component: ClientShippingBasicInfoComponent,
            // },
            // {
            //     path: 'shipping/basic-info-2',
            //     component: ClientShippingBasicInfo2Component,
            // },
            // {
            //     path: 'shipping/pickup-address',
            //     component: ClientShippingPickupAddressComponent,
            // },
            // {
            //     path: 'shipping/delivery-address',
            //     component: ClientShippingDeliveryAddressComponent,
            // },
            {
                path: "",
                redirectTo: "dashboard",
                pathMatch: "full"
            }
        ]
    }
];
