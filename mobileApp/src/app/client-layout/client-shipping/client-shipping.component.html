<div class="shipping-page">
  <!-- Custom Header with curved design -->
  <div class="shipping-header-section">
    <app-customer-header [innerPage]="false" [headingText]="'Shipping Management'"
      [rightAction]="true"></app-customer-header>

  </div>
  <!-- Search and Add Section -->
  <div class="search-add-section">
    <div class="search-container">
      <ion-item class="search-input-item" lines="none">
        <ion-icon name="search" slot="start" class="search-icon"></ion-icon>
        <ion-input placeholder="Search here..." [(ngModel)]="searchTerm" (ionInput)="onSearchChange($event)"
          [debounce]="300" class="search-input">
        </ion-input>
      </ion-item>
    </div>
    <div class="add-button-container">
      <ion-button class="add-button" (click)="addShipment()">
        <ion-icon name="add" slot="start"></ion-icon>
        Add
      </ion-button>
    </div>
  </div>

  <ion-content class="shipping-content" [scrollEvents]="true" [forceOverscroll]="false">

    <!-- Shipment Cards -->
    <div class="shipment-cards">
      <!-- Dynamic Shipment Cards -->
      <div *ngFor="let shipment of paginatedShipments" class="shipment-card"
        [ngClass]="{'featured': shipment.isFeatured}">
        <div class="card-header">
          <div class="ref-info">
            <span class="ref-label">Ref. ID</span>
            <span class="ref-number">{{shipment.refId}}</span>
          </div>
          <div class="status-indicators">
            <div class="location-indicator">
              <ion-icon name="location" class="location-icon"></ion-icon>
            </div>
            <div class="edit-indicator">
              <ion-icon name="create-outline" class="edit-icon"></ion-icon>
            </div>
          </div>
        </div>

        <div class="card-content">
          <div class="contact-info">
            <div class="contact-name">Driver Name</div>
            <div class="person-name">{{shipment.driverName}}</div>
            <div class="contact-phone">Driver Phone</div>
            <div class="phone-number">{{shipment.driverPhone}}</div>
          </div>

          <div class="location-info">
            <div class="from-section">
              <span class="location-label">From</span>
              <span class="location-value">{{shipment.fromLocation}}</span>
            </div>
            <div class="to-section">
              <span class="location-label">To</span>
              <span class="location-value">{{shipment.toLocation}}</span>
            </div>
          </div>
        </div>

        <div class="card-footer">
          <div class="status-section">
            <span class="status-label">Status</span>
            <span class="status-value" [ngClass]="getStatusClass(shipment.status)">
              {{shipment.status}}
            </span>
          </div>
          <div class="date-section">
            <span class="date-label">ETD</span>
            <span class="date-value">{{shipment.etd}}</span>
          </div>
        </div>
      </div>

      <!-- No Results Message -->
      <div *ngIf="paginatedShipments.length === 0" class="no-results">
        <ion-icon name="car-outline" class="no-results-icon"></ion-icon>
        <p class="no-results-text">No shipments found</p>
        <p class="no-results-subtext">Try adjusting your search criteria</p>
      </div>
    </div>

    <!-- Pagination -->
    <div class="pagination-section" *ngIf="totalPages > 1">
      <ion-button fill="clear" class="pagination-btn" [disabled]="currentPage === 1" (click)="previousPage()">
        <ion-icon name="chevron-back" slot="icon-only"></ion-icon>
      </ion-button>

      <div class="page-numbers">
        <span *ngFor="let pageNum of pageNumbers" class="page-number"
          [ngClass]="{'active': pageNum === currentPage, 'ellipsis': pageNum === -1}"
          (click)="pageNum !== -1 && goToPage(pageNum)">
          {{pageNum === -1 ? '...' : pageNum}}
        </span>
      </div>

      <ion-button fill="clear" class="pagination-btn" [disabled]="currentPage === totalPages" (click)="nextPage()">
        <ion-icon name="chevron-forward" slot="icon-only"></ion-icon>
      </ion-button>
    </div>

    <!-- Results Info -->
    <div class="results-info" *ngIf="filteredShipments.length > 0">
      <p class="results-text">
        Showing {{((currentPage - 1) * itemsPerPage) + 1}} to
        {{Math.min(currentPage * itemsPerPage, filteredShipments.length)}}
        of {{filteredShipments.length}} shipments
      </p>
    </div>
  </ion-content>
</div>