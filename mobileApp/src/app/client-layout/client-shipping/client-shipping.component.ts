import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';

// Interface for Shipment Item
interface ShipmentItem {
  id: string;
  refId: string;
  driverName: string;
  driverPhone: string;
  fromLocation: string;
  toLocation: string;
  etd: string;
  paymentType: string;
  status: string;
  isFeatured?: boolean;
}

@Component({
  selector: 'app-client-shipping',
  templateUrl: './client-shipping.component.html',
  styleUrls: ['./client-shipping.component.scss'],
  standalone: false
})
export class ClientShippingComponent implements OnInit {

  // Data arrays
  shipments: ShipmentItem[] = [];
  filteredShipments: ShipmentItem[] = [];
  paginatedShipments: ShipmentItem[] = [];

  // Pagination properties
  currentPage: number = 1;
  itemsPerPage: number = 5;
  totalPages: number = 0;
  pageNumbers: number[] = [];

  // Search
  searchTerm: string = '';

  // Filter
  showFilter: boolean = false;

  // Make Math available in template
  Math = Math;

  constructor(private readonly navController: NavController) { }

  ngOnInit() {
    this.loadSampleData();
    this.applyFiltersAndPagination();
  }

  // Load sample shipment data
  loadSampleData() {
    this.shipments = [
      {
        id: '1',
        refId: '#15486524',
        driverName: 'John Smith',
        driverPhone: '(*************',
        fromLocation: 'Alberta, CA',
        toLocation: 'Red Deer',
        etd: '25-03-25, 11:30pm',
        paymentType: 'Prepaid',
        status: 'Pending',
        isFeatured: true
      },
      {
        id: '2',
        refId: '#15486525',
        driverName: 'Sarah Johnson',
        driverPhone: '(*************',
        fromLocation: 'Vancouver, BC',
        toLocation: 'Calgary, AB',
        etd: '26-03-25, 09:15am',
        paymentType: 'COD',
        status: 'In Transit',
      },
      {
        id: '3',
        refId: '#15486526',
        driverName: 'Mike Wilson',
        driverPhone: '(*************',
        fromLocation: 'Toronto, ON',
        toLocation: 'Montreal, QC',
        etd: '27-03-25, 02:45pm',
        paymentType: 'Prepaid',
        status: 'Delivered',
      },
      {
        id: '4',
        refId: '#15486527',
        driverName: 'Emily Davis',
        driverPhone: '(*************',
        fromLocation: 'Edmonton, AB',
        toLocation: 'Winnipeg, MB',
        etd: '28-03-25, 08:30am',
        paymentType: 'COD',
        status: 'Pending',
      },
      {
        id: '5',
        refId: '#15486528',
        driverName: 'David Brown',
        driverPhone: '(*************',
        fromLocation: 'Ottawa, ON',
        toLocation: 'Quebec City, QC',
        etd: '29-03-25, 04:20pm',
        paymentType: 'Prepaid',
        status: 'Cancelled',
      },
      {
        id: '6',
        refId: '#15486529',
        driverName: 'Lisa Anderson',
        driverPhone: '(*************',
        fromLocation: 'Halifax, NS',
        toLocation: 'Fredericton, NB',
        etd: '30-03-25, 10:15am',
        paymentType: 'COD',
        status: 'In Transit',
      },
      {
        id: '7',
        refId: '#15486530',
        driverName: 'Robert Taylor',
        driverPhone: '(*************',
        fromLocation: 'Regina, SK',
        toLocation: 'Saskatoon, SK',
        etd: '31-03-25, 01:45pm',
        paymentType: 'Prepaid',
        status: 'Delivered',
      },
      {
        id: '8',
        refId: '#15486531',
        driverName: 'Jennifer White',
        driverPhone: '(*************',
        fromLocation: 'Victoria, BC',
        toLocation: 'Kelowna, BC',
        etd: '01-04-25, 07:30am',
        paymentType: 'COD',
        status: 'Pending',
      },
      {
        id: '9',
        refId: '#15486532',
        driverName: 'Christopher Lee',
        driverPhone: '(*************',
        fromLocation: 'Thunder Bay, ON',
        toLocation: 'Sudbury, ON',
        etd: '02-04-25, 03:15pm',
        paymentType: 'Prepaid',
        status: 'In Transit',
      },
      {
        id: '10',
        refId: '#15486533',
        driverName: 'Amanda Garcia',
        driverPhone: '(*************',
        fromLocation: 'St. John\'s, NL',
        toLocation: 'Corner Brook, NL',
        etd: '03-04-25, 11:45am',
        paymentType: 'COD',
        status: 'Delivered',
      },
      {
        id: '11',
        refId: '#15486534',
        driverName: 'Kevin Martinez',
        driverPhone: '(*************',
        fromLocation: 'Charlottetown, PE',
        toLocation: 'Summerside, PE',
        etd: '04-04-25, 05:20pm',
        paymentType: 'Prepaid',
        status: 'Pending',
      },
      {
        id: '12',
        refId: '#15486535',
        driverName: 'Michelle Rodriguez',
        driverPhone: '(*************',
        fromLocation: 'Yellowknife, NT',
        toLocation: 'Whitehorse, YT',
        etd: '05-04-25, 09:30am',
        paymentType: 'COD',
        status: 'Cancelled',
      }
    ];
  }

  // Apply search filter and pagination
  applyFiltersAndPagination() {
    // Apply search filter
    if (this.searchTerm.trim()) {
      this.filteredShipments = this.shipments.filter(shipment =>
        shipment.driverName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        shipment.refId.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        shipment.fromLocation.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        shipment.toLocation.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        shipment.status.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    } else {
      this.filteredShipments = [...this.shipments];
    }

    // Calculate pagination
    this.totalPages = Math.ceil(this.filteredShipments.length / this.itemsPerPage);
    this.generatePageNumbers();
    this.updatePaginatedData();
  }

  // Generate page numbers for pagination
  generatePageNumbers() {
    this.pageNumbers = [];
    const maxVisiblePages = 7;

    if (this.totalPages <= maxVisiblePages) {
      for (let i = 1; i <= this.totalPages; i++) {
        this.pageNumbers.push(i);
      }
    } else {
      // Show first page, current page range, and last page
      if (this.currentPage <= 4) {
        for (let i = 1; i <= 5; i++) {
          this.pageNumbers.push(i);
        }
        this.pageNumbers.push(-1); // Ellipsis
        this.pageNumbers.push(this.totalPages);
      } else if (this.currentPage >= this.totalPages - 3) {
        this.pageNumbers.push(1);
        this.pageNumbers.push(-1); // Ellipsis
        for (let i = this.totalPages - 4; i <= this.totalPages; i++) {
          this.pageNumbers.push(i);
        }
      } else {
        this.pageNumbers.push(1);
        this.pageNumbers.push(-1); // Ellipsis
        for (let i = this.currentPage - 1; i <= this.currentPage + 1; i++) {
          this.pageNumbers.push(i);
        }
        this.pageNumbers.push(-1); // Ellipsis
        this.pageNumbers.push(this.totalPages);
      }
    }
  }

  // Update paginated data based on current page
  updatePaginatedData() {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    this.paginatedShipments = this.filteredShipments.slice(startIndex, endIndex);
  }

  // Navigate to specific page
  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.applyFiltersAndPagination();
    }
  }

  // Navigate to previous page
  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.applyFiltersAndPagination();
    }
  }

  // Navigate to next page
  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.applyFiltersAndPagination();
    }
  }

  // Handle search
  onSearchChange(event: any) {
    this.searchTerm = event.detail.value || '';
    this.currentPage = 1; // Reset to first page when searching
    this.applyFiltersAndPagination();
  }

  // Get status class for styling
  getStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'pending': return 'pending';
      case 'in transit': return 'in-transit';
      case 'delivered': return 'delivered';
      case 'cancelled': return 'cancelled';
      default: return 'pending';
    }
  }

  // Navigate to add shipment (basic information)
  addShipment() {
    this.navController.navigateForward('/client-portal/shipping/basic-info');
  }

  // Go back to previous page
  goBack() {
    this.navController.back();
  }

  // Toggle filter visibility
  toggleFilter() {
    this.showFilter = !this.showFilter;
    // Add filter logic here if needed
  }

  // Edit shipment
  editShipment(shipment: ShipmentItem) {
    // Navigate to edit page with shipment data
    this.navController.navigateForward('/client-portal/shipping/basic-info', {
      queryParams: { id: shipment.id, mode: 'edit' }
    });
  }

  // Delete shipment
  deleteShipment(shipment: ShipmentItem) {
    // Show confirmation dialog and delete shipment
    const index = this.shipments.findIndex(s => s.id === shipment.id);
    if (index > -1) {
      this.shipments.splice(index, 1);
      this.applyFiltersAndPagination();
    }
  }

  // View shipment location
  viewLocation(shipment: ShipmentItem) {
    // Navigate to location/tracking page
  }

}
