<ion-content class="home-page">

    <div class="dashboard-header-section">
        <div class="dashboard-header-container">
            <div class="dashboard-icon-container">
                <div class="dashboard-user-info">
                    <span class="username">Hello,<strong> {{user?.firstName}} {{user?.lastName}} </strong> </span>
                    <span class="location">Location</span>
                </div>
                <div class="dashboard-icon-background margin-right-10">
                    <ion-icon class="bell-icon" src="/assets/images/svg/bell.svg"></ion-icon>
                </div>
                <div class="dashboard-icon-background" (click)="logout()">
                    <ion-icon class="logout-icon" src="/assets/images/svg/logout-icon.svg"></ion-icon>
                </div>
            </div>

            <div class="fixed-search home-page-tracking margin-top-15">
                <div class="padding-top">
                    <ion-item class="site-form-control search-form-control" lines="none">
                        <i-feather class="map-pin-icon start-icon" name="Search" slot="start"></i-feather>
                        <ion-input class="search-ion-input" label="Tracking Number" labelPlacement="floating"
                            name="trackingNumber" [debounce]="500"></ion-input>
                    </ion-item>
                </div>
                <div class="tracking-container margin-right-10">
                    <ion-icon class="tracking-icon" src="\assets\images\svg\barcode.svg" slot="end"></ion-icon>
                </div>
            </div>
        </div>
    </div>

    <div class="home-page-body-section">
        <span class="logistics-text">Client Logistic Management</span>
        <div class="logistics-management-container">
            <!-- Left Column -->
            <div class="logistics-management">
                <div class="total-shipment-container">
                    <ion-icon class="shipment-icon" src="/assets/images/svg/ship-approve.svg"></ion-icon>
                    <span class="shipment-text">Total Shipment</span>
                    <span class="shipment-count">10</span>
                </div>
                <div class="total-shipment-container progress-shipments">
                    <ion-icon class="shipment-icon" src="/assets/images/svg/half-time.svg"></ion-icon>
                    <span class="shipment-text">In Progress</span>
                    <span class="shipment-count">04</span>
                </div>
            </div>

            <!-- Right Column (offset down) -->
            <div class="logistics-management">
                <div class="total-shipment-container upcoming-shipments">
                    <ion-icon class="shipment-icon" src="/assets/images/svg/ship-delivery.svg"></ion-icon>
                    <span class="shipment-text">New Upcoming Shipments</span>
                    <span class="shipment-count">08</span>
                </div>
                <div class="total-shipment-container complete-shipments">
                    <ion-icon class="shipment-icon" src="/assets/images/svg/sent.svg"></ion-icon>
                    <span class="shipment-text">Complete Shipments</span>
                    <span class="shipment-count">10</span>
                </div>
            </div>
        </div>

    </div>
</ion-content>