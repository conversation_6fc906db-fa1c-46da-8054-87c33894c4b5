import { ChangeDetectorRef, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { UserStateService } from 'src/services/user-state.service';
import { UserProfileService } from 'src/services/user-profile.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-client-home',
  templateUrl: './client-home.component.html',
  styleUrls: ['./client-home.component.scss'],
  standalone: false,
  encapsulation: ViewEncapsulation.None
})
export class ClientHomeComponent implements OnInit, OnDestroy {

  user: any;
  userDisplayName: string = '';
  private subscriptions = new Subscription();

  constructor(
    private readonly navController: NavController,
    private readonly localStorageService: LocalStorageService,
    private readonly authService: AuthService,
    private readonly dataService: DataService,
    private readonly changeDetectorRef: ChangeDetectorRef,
    public commonService: CommonService,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    private readonly route: ActivatedRoute,
    private router: Router,
    private readonly userStateService: UserStateService,
    private readonly userProfileService: UserProfileService
  ) {
  }

  ngOnInit() {
    // Subscribe to user display name changes
    this.subscriptions.add(
      this.userStateService.userDisplayName$.subscribe(displayName => {
        this.userDisplayName = displayName;
        // Update the user object for backward compatibility
        if (displayName) {
          const names = displayName.split(' ');
          this.user = {
            firstName: names[0] || '',
            lastName: names.slice(1).join(' ') || ''
          };
        }
      })
    );
  }

  ionViewWillEnter() {
    // Load fresh profile data when entering the dashboard
    this.loadUserProfile();

    // Fallback to auth service if global state is empty
    if (!this.userStateService.isProfileLoaded()) {
      this.user = this.authService.getUser();
    }
  }

  private loadUserProfile(): void {
    // Silently load profile data without showing loading indicator
    this.subscriptions.add(
      this.userProfileService.refreshUserProfile().subscribe({
        next: (response) => {
          console.log('Client Dashboard: Profile refreshed successfully');
        },
        error: (error) => {
          console.warn('Client Dashboard: Failed to refresh profile:', error);
          // Fallback to auth service data
          this.user = this.authService.getUser();
        }
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  async logout(): Promise<void> {
    try {
      // Show loading indicator
      this.loadingService.show();

      // Clear user profile state
      this.userStateService.clearUserProfile();

      // Call auth service logout (this will clear local storage and send logout event)
      this.authService.logout();

      // Hide loading indicator
      this.loadingService.hide();

      // Navigate to login screen and clear all routes
      await this.navController.navigateRoot('/account/login', {
        animated: true,
        animationDirection: 'back'
      });

      // Show success message
      this.toastService.show('Logged out successfully');

    } catch (error) {
      console.error('Logout error:', error);

      // Hide loading indicator in case of error
      this.loadingService.hide();

      // Still try to logout locally and navigate to login
      this.authService.clearAllAuthData();
      this.userStateService.clearUserProfile();

      await this.navController.navigateRoot('/account/login', {
        animated: true,
        animationDirection: 'back'
      });

      this.toastService.show('Logged out locally');
    }
  }

}
