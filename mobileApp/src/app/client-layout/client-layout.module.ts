import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { MaskitoDirective } from '@maskito/angular';
import { ImageCropperModule } from 'ngx-image-cropper';

import { SharedModuleModule } from 'src/shared/shared-module.module';
import { IconsModule } from 'src/shared/icon.module';
import { ClientLayoutComponent } from './client-layout.component';
import { CLIENTLAYOUTROUTING } from './client-layout.routing';
import { ClientHomeComponent } from './client-home/client-home.component';
import { ClientShippingComponent } from './client-shipping/client-shipping.component';
import { ClientQuotationComponent } from './client-quotation/client-quotation.component';
import { ClientQuotationBasicInfoComponent } from './client-quotation-basic-info/client-quotation-basic-info.component';
import { ClientCalendarComponent } from './client-calendar/client-calendar.component';
import { ClientAccountComponent } from './client-account/client-account.component';


// Import client shipping sub-components (commented out until created)
// import { ClientShippingBasicInfoComponent } from './client-shipping-basic-info/client-shipping-basic-info.component';
// import { ClientShippingBasicInfo2Component } from './client-shipping-basic-info-2/client-shipping-basic-info-2.component';
// import { ClientShippingPickupAddressComponent } from './client-shipping-pickup-address/client-shipping-pickup-address.component';
// import { ClientShippingDeliveryAddressComponent } from './client-shipping-delivery-address/client-shipping-delivery-address.component';

@NgModule({
  declarations: [
    ClientLayoutComponent,
    ClientHomeComponent,
    ClientShippingComponent,
    ClientQuotationComponent,
    ClientQuotationBasicInfoComponent,
    ClientCalendarComponent,
    ClientAccountComponent,

    // Client shipping sub-components (commented out until created)
    // ClientShippingBasicInfoComponent,
    // ClientShippingBasicInfo2Component,
    // ClientShippingPickupAddressComponent,
    // ClientShippingDeliveryAddressComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    MaskitoDirective,
    IconsModule,
    RouterModule.forChild(CLIENTLAYOUTROUTING),
    SharedModuleModule,
    ImageCropperModule
  ],
  providers: [],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ClientLayoutModule { }
