.quotation-page {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;

    // Header Section
    .quotation-header-section {
        background: linear-gradient(135deg, #FFEA00 0%, #FFD700 100%);
        position: relative;
        z-index: 10;
        animation: headerFloat 3s ease-in-out infinite;

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: env(safe-area-inset-top, 0px) 20px 0 20px;
            height: 120px;
            position: relative;
            z-index: 2;

            .filter-button {
                --color: #000;
                --background: transparent;
                width: 44px;
                height: 44px;
                border-radius: 50%;
                margin: 0;

                ion-icon {
                    font-size: 24px;
                    color: #000;
                }

                &:hover {
                    --background: rgba(0, 0, 0, 0.1);
                }
            }

            .header-title {
                font-size: 20px;
                font-weight: 700;
                color: #000;
                text-align: center;
                flex: 1;
                margin: 0 16px;
            }
        }

        .curved-bottom {
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 30px;
            background: linear-gradient(135deg, #FFEA00 0%, #FFD700 100%);
            border-radius: 0 0 50% 50%;
            animation: curvedFloat 3s ease-in-out infinite;
        }
    }

    .search-add-section {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 20px;
        background: #f8f9fa;
        margin-top: -15px;
        position: relative;
        z-index: 5;

        .search-container {
            flex: 1;

            .custom-searchbar {
                --background: #ffffff;
                --border-radius: 25px;
                --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                --placeholder-color: #999;
                --color: #333;
                height: 50px;
                --padding-start: 16px;
                --padding-end: 16px;
                border: 1px solid #e0e0e0;
            }
        }

        .add-button {
            --background: #000;
            --color: #fff;
            --border-radius: 25px;
            height: 50px;
            min-width: 80px;
            font-weight: 600;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

            ion-icon {
                margin-right: 8px;
                font-size: 18px;
            }

            &:hover {
                --background: #333;
                transform: translateY(-2px);
                box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
            }
        }
    }

    .quotation-content {
        flex: 1;
        --background: #f8f9fa;
        --padding-top: 0;
        --padding-bottom: 20px;
        --padding-start: 20px;
        --padding-end: 20px;
        position: relative;
        z-index: 1;

        .quotation-cards {
            display: flex;
            flex-direction: column;
            gap: 16px;
            margin-bottom: 30px;

            .quotation-card {
                background: #ffffff;
                border-radius: 20px;
                padding: 20px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                border: 1px solid #f0f0f0;
                margin-bottom: 16px;
                position: relative;

                &.featured {
                    border: 2px solid #FFEA00;
                    box-shadow: 0 6px 20px rgba(255, 234, 0, 0.25);
                    background: linear-gradient(135deg, #fffef7 0%, #ffffff 100%);
                }

                .card-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: 16px;

                    .ref-info {
                        .ref-label {
                            display: block;
                            font-size: 12px;
                            color: #666;
                            margin-bottom: 4px;
                        }

                        .ref-number {
                            font-size: 16px;
                            font-weight: 600;
                            color: #333;
                        }
                    }

                    .status-indicators {
                        display: flex;
                        gap: 8px;

                        .location-indicator,
                        .edit-indicator {
                            width: 36px;
                            height: 36px;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
                        }

                        .location-indicator {
                            background: linear-gradient(135deg, #e8f5e8 0%, #d4f4d4 100%);

                            .location-icon {
                                color: #4CAF50;
                                font-size: 18px;
                            }
                        }

                        .edit-indicator {
                            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b3 100%);

                            .edit-icon {
                                color: #FF9800;
                                font-size: 18px;
                            }
                        }
                    }
                }

                .card-content {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 16px;

                    .contact-info {
                        flex: 1;

                        .contact-name,
                        .contact-phone {
                            font-size: 12px;
                            color: #666;
                            margin-bottom: 4px;
                        }

                        .person-name,
                        .phone-number {
                            font-size: 14px;
                            font-weight: 500;
                            color: #333;
                            margin-bottom: 12px;
                        }
                    }

                    .location-info {
                        text-align: right;

                        .from-section,
                        .to-section {
                            margin-bottom: 12px;

                            .location-label {
                                display: block;
                                font-size: 12px;
                                color: #666;
                                margin-bottom: 4px;
                            }

                            .location-value {
                                font-size: 14px;
                                font-weight: 500;
                                color: #333;
                            }
                        }
                    }
                }

                .card-footer {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding-top: 16px;
                    border-top: 1px solid #f0f0f0;

                    .status-section,
                    .date-section {

                        .status-label,
                        .date-label {
                            display: block;
                            font-size: 12px;
                            color: #666;
                            margin-bottom: 4px;
                        }

                        .status-value {
                            font-size: 14px;
                            font-weight: 600;
                            padding: 4px 12px;
                            border-radius: 12px;

                            &.interested {
                                background: #e8f5e8;
                                color: #4CAF50;
                            }

                            &.pending {
                                background: #fff3e0;
                                color: #FF9800;
                            }

                            &.approved {
                                background: #e3f2fd;
                                color: #2196F3;
                            }

                            &.rejected {
                                background: #ffebee;
                                color: #f44336;
                            }
                        }

                        .date-value {
                            font-size: 14px;
                            font-weight: 500;
                            color: #333;
                        }
                    }
                }
            }

            // No results message
            .no-results {
                text-align: center;
                padding: 60px 20px;
                color: #666;

                .no-results-icon {
                    font-size: 64px;
                    color: #ccc;
                    margin-bottom: 16px;
                }

                .no-results-text {
                    font-size: 18px;
                    font-weight: 600;
                    margin-bottom: 8px;
                    color: #333;
                }

                .no-results-subtext {
                    font-size: 14px;
                    color: #666;
                    margin: 0;
                }
            }
        }

        .pagination-section {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 16px;
            padding: 30px 0 20px 0;

            .pagination-btn {
                --color: #666;
                --background: #ffffff;
                width: 44px;
                height: 44px;
                border-radius: 50%;
                border: 1px solid #e0e0e0;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);

                ion-icon {
                    font-size: 20px;
                }

                &:disabled {
                    --color: #ccc;
                    opacity: 0.5;
                    cursor: not-allowed;
                    box-shadow: none;
                }

                &:not(:disabled):hover {
                    --background: #f8f9fa;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                }
            }

            .page-numbers {
                display: flex;
                gap: 8px;

                .page-number {
                    width: 36px;
                    height: 36px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 50%;
                    font-size: 15px;
                    font-weight: 500;
                    color: #666;
                    cursor: pointer;
                    background: #ffffff;
                    border: 1px solid #e0e0e0;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
                    transition: all 0.2s ease;

                    &.active {
                        background: #FFEA00;
                        color: #000;
                        font-weight: 700;
                        border-color: #FFEA00;
                        box-shadow: 0 4px 12px rgba(255, 234, 0, 0.3);
                        transform: scale(1.1);
                    }

                    &.ellipsis {
                        cursor: default;
                        pointer-events: none;
                        background: transparent;
                        border: none;
                        box-shadow: none;
                        color: #999;
                    }

                    &:not(.ellipsis):not(.active):hover {
                        background: #f8f9fa;
                        border-color: #FFEA00;
                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
                        transform: translateY(-1px);
                    }
                }
            }
        }

        .results-info {
            text-align: center;
            padding: 10px 0 20px 0;

            .results-text {
                font-size: 14px;
                color: #666;
                margin: 0;
            }
        }
    }
}

// Parallax Animations
@keyframes headerFloat {

    0%,
    100% {
        transform: translateY(0px) scale(1);
        box-shadow: 0 4px 20px rgba(255, 234, 0, 0.25);
    }

    50% {
        transform: translateY(-1px) scale(1.002);
        box-shadow: 0 6px 25px rgba(255, 234, 0, 0.3);
    }
}

@keyframes curvedFloat {

    0%,
    100% {
        transform: translateY(0px) scaleX(1);
        border-radius: 0 0 50% 50%;
    }

    50% {
        transform: translateY(-1px) scaleX(1.005);
        border-radius: 0 0 48% 48%;
    }
}