import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { NavC<PERSON>roller, IonContent } from '@ionic/angular';

interface QuotationItem {
  id: string;
  refId: string;
  contactPersonName: string;
  phoneNumber: string;
  fromLocation: string;
  toLocation: string;
  status: 'interested' | 'pending' | 'approved' | 'rejected';
  quotationDate: string;
  isFeatured?: boolean;
}

@Component({
  selector: 'app-client-quotation',
  templateUrl: './client-quotation.component.html',
  styleUrls: ['./client-quotation.component.scss'],
  standalone: false
})
export class ClientQuotationComponent implements OnInit {

  quotations: QuotationItem[] = [];
  filteredQuotations: QuotationItem[] = [];
  paginatedQuotations: QuotationItem[] = [];

  // Pagination properties
  currentPage: number = 1;
  itemsPerPage: number = 5;
  totalPages: number = 0;
  pageNumbers: number[] = [];

  // Search
  searchTerm: string = '';

  // Make Math available in template
  Math = Math;

  constructor(private readonly navController: NavController) { }

  ngOnInit() {
    this.loadSampleData();
    this.applyFiltersAndPagination();
  }

  // Load sample quotation data
  loadSampleData() {
    this.quotations = [
      {
        id: '1',
        refId: '#15486524',
        contactPersonName: 'John Smith',
        phoneNumber: '(*************',
        fromLocation: 'Alberta, CA',
        toLocation: 'Red Deer',
        status: 'interested',
        quotationDate: '01-08-2025',
        isFeatured: true
      },
      {
        id: '2',
        refId: '#15486525',
        contactPersonName: 'Sarah Johnson',
        phoneNumber: '(*************',
        fromLocation: 'Vancouver, BC',
        toLocation: 'Calgary, AB',
        status: 'pending',
        quotationDate: '02-08-2025'
      },
      {
        id: '3',
        refId: '#15486526',
        contactPersonName: 'Mike Wilson',
        phoneNumber: '(*************',
        fromLocation: 'Toronto, ON',
        toLocation: 'Montreal, QC',
        status: 'approved',
        quotationDate: '03-08-2025'
      },
      {
        id: '4',
        refId: '#15486527',
        contactPersonName: 'Emily Davis',
        phoneNumber: '(*************',
        fromLocation: 'Edmonton, AB',
        toLocation: 'Winnipeg, MB',
        status: 'interested',
        quotationDate: '04-08-2025'
      },
      {
        id: '5',
        refId: '#15486528',
        contactPersonName: 'David Brown',
        phoneNumber: '(*************',
        fromLocation: 'Ottawa, ON',
        toLocation: 'Quebec City, QC',
        status: 'rejected',
        quotationDate: '05-08-2025'
      },
      {
        id: '6',
        refId: '#15486529',
        contactPersonName: 'Lisa Anderson',
        phoneNumber: '(*************',
        fromLocation: 'Halifax, NS',
        toLocation: 'Fredericton, NB',
        status: 'pending',
        quotationDate: '06-08-2025'
      },
      {
        id: '7',
        refId: '#15486530',
        contactPersonName: 'Robert Taylor',
        phoneNumber: '(*************',
        fromLocation: 'Regina, SK',
        toLocation: 'Saskatoon, SK',
        status: 'interested',
        quotationDate: '07-08-2025'
      },
      {
        id: '8',
        refId: '#15486531',
        contactPersonName: 'Jennifer White',
        phoneNumber: '(*************',
        fromLocation: 'Victoria, BC',
        toLocation: 'Kelowna, BC',
        status: 'approved',
        quotationDate: '08-08-2025'
      },
      {
        id: '9',
        refId: '#15486532',
        contactPersonName: 'Christopher Lee',
        phoneNumber: '(*************',
        fromLocation: 'Thunder Bay, ON',
        toLocation: 'Sudbury, ON',
        status: 'pending',
        quotationDate: '09-08-2025'
      },
      {
        id: '10',
        refId: '#15486533',
        contactPersonName: 'Amanda Garcia',
        phoneNumber: '(*************',
        fromLocation: 'St. John\'s, NL',
        toLocation: 'Corner Brook, NL',
        status: 'interested',
        quotationDate: '10-08-2025'
      },
      {
        id: '11',
        refId: '#15486534',
        contactPersonName: 'Kevin Martinez',
        phoneNumber: '(*************',
        fromLocation: 'Charlottetown, PE',
        toLocation: 'Summerside, PE',
        status: 'approved',
        quotationDate: '11-08-2025'
      },
      {
        id: '12',
        refId: '#15486535',
        contactPersonName: 'Michelle Rodriguez',
        phoneNumber: '(*************',
        fromLocation: 'Yellowknife, NT',
        toLocation: 'Whitehorse, YT',
        status: 'rejected',
        quotationDate: '12-08-2025'
      }
    ];
  }

  // Apply search filter and pagination
  applyFiltersAndPagination() {
    // Apply search filter
    if (this.searchTerm.trim()) {
      this.filteredQuotations = this.quotations.filter(quotation =>
        quotation.contactPersonName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        quotation.refId.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        quotation.fromLocation.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        quotation.toLocation.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    } else {
      this.filteredQuotations = [...this.quotations];
    }

    // Calculate pagination
    this.totalPages = Math.ceil(this.filteredQuotations.length / this.itemsPerPage);
    this.generatePageNumbers();
    this.updatePaginatedData();
  }

  // Generate page numbers for pagination
  generatePageNumbers() {
    this.pageNumbers = [];
    const maxVisiblePages = 7;

    if (this.totalPages <= maxVisiblePages) {
      for (let i = 1; i <= this.totalPages; i++) {
        this.pageNumbers.push(i);
      }
    } else {
      // Show first page, current page range, and last page
      if (this.currentPage <= 4) {
        for (let i = 1; i <= 5; i++) {
          this.pageNumbers.push(i);
        }
        this.pageNumbers.push(-1); // Ellipsis
        this.pageNumbers.push(this.totalPages);
      } else if (this.currentPage >= this.totalPages - 3) {
        this.pageNumbers.push(1);
        this.pageNumbers.push(-1); // Ellipsis
        for (let i = this.totalPages - 4; i <= this.totalPages; i++) {
          this.pageNumbers.push(i);
        }
      } else {
        this.pageNumbers.push(1);
        this.pageNumbers.push(-1); // Ellipsis
        for (let i = this.currentPage - 1; i <= this.currentPage + 1; i++) {
          this.pageNumbers.push(i);
        }
        this.pageNumbers.push(-1); // Ellipsis
        this.pageNumbers.push(this.totalPages);
      }
    }
  }

  // Update paginated data based on current page
  updatePaginatedData() {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    this.paginatedQuotations = this.filteredQuotations.slice(startIndex, endIndex);
  }

  // Navigate to specific page
  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.applyFiltersAndPagination();
    }
  }

  // Navigate to previous page
  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.applyFiltersAndPagination();
    }
  }

  // Navigate to next page
  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.applyFiltersAndPagination();
    }
  }

  // Handle search
  onSearchChange(event: any) {
    this.searchTerm = event.detail.value || '';
    this.currentPage = 1; // Reset to first page when searching
    this.applyFiltersAndPagination();
  }

  // Get status class for styling
  getStatusClass(status: string): string {
    switch (status) {
      case 'interested': return 'interested';
      case 'pending': return 'pending';
      case 'approved': return 'approved';
      case 'rejected': return 'rejected';
      default: return 'pending';
    }
  }

  // Navigate to add quotation (basic information)
  addQuotation() {
    this.navController.navigateForward('/client-portal/quotation/basic-info');
  }

}
