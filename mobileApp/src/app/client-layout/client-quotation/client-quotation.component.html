<div class="quotation-page">
  <!-- Custom Header with curved design -->
  <app-customer-header [innerPage]="false" [headingText]="'Quotation Management'"
    [rightAction]="true"></app-customer-header>
  <!-- Search and Add Section -->
  <div class="fixed-search quotation-page-search">
    <div class="padding-top">
      <ion-item class="site-form-control search-form-control" lines="none">
        <i-feather class="map-pin-icon start-icon" name="Search" slot="start"></i-feather>
        <ion-input class="search-ion-input" label="Search here..." labelPlacement="floating" name="searchItem"
          [debounce]="500" (ionInput)="onSearchChange($event)"></ion-input>
      </ion-item>
    </div>
    <div class=" add-shipping-container" (click)="addQuotation()">
      <ion-icon class="add-shipping-icon" src="\assets\images\svg\add-icon.svg" slot="start"></ion-icon>
      <span class="add-shipping-text">Add</span>
    </div>
  </div>

  <ion-content class="quotation-content" [scrollEvents]="true" [forceOverscroll]="false">

    <!-- Quotation Cards -->
    <div class="quotation-cards">
      <!-- Dynamic Quotation Cards -->
      <div *ngFor="let quotation of paginatedQuotations" class="quotation-card"
        [ngClass]="{'featured': quotation.isFeatured}">
        <div class="card-header">
          <div class="ref-info">
            <span class="ref-label">Ref. ID</span>
            <span class="ref-number">{{quotation.refId}}</span>
          </div>
          <div class="status-indicators">
            <div class="location-indicator">
              <ion-icon name="location" class="location-icon"></ion-icon>
            </div>
            <div class="edit-indicator">
              <ion-icon name="create-outline" class="edit-icon"></ion-icon>
            </div>
          </div>
        </div>

        <div class="card-content">
          <div class="contact-info">
            <div class="contact-name">Contact Person Name</div>
            <div class="person-name">{{quotation.contactPersonName}}</div>
            <div class="contact-phone">Contact Person Phone</div>
            <div class="phone-number">{{quotation.phoneNumber}}</div>
          </div>

          <div class="location-info">
            <div class="from-section">
              <span class="location-label">From</span>
              <span class="location-value">{{quotation.fromLocation}}</span>
            </div>
            <div class="to-section">
              <span class="location-label">To</span>
              <span class="location-value">{{quotation.toLocation}}</span>
            </div>
          </div>
        </div>

        <div class="card-footer">
          <div class="status-section">
            <span class="status-label">Status</span>
            <span class="status-value" [ngClass]="getStatusClass(quotation.status)">
              {{quotation.status | titlecase}}
            </span>
          </div>
          <div class="date-section">
            <span class="date-label">Quotation Date</span>
            <span class="date-value">{{quotation.quotationDate}}</span>
          </div>
        </div>
      </div>

      <!-- No Results Message -->
      <div *ngIf="paginatedQuotations.length === 0" class="no-results">
        <ion-icon name="document-outline" class="no-results-icon"></ion-icon>
        <p class="no-results-text">No quotations found</p>
        <p class="no-results-subtext">Try adjusting your search criteria</p>
      </div>
    </div>

    <!-- Pagination -->
    <div class="pagination-section" *ngIf="totalPages > 1">
      <ion-button fill="clear" class="pagination-btn" [disabled]="currentPage === 1" (click)="previousPage()">
        <ion-icon name="chevron-back" slot="icon-only"></ion-icon>
      </ion-button>

      <div class="page-numbers">
        <span *ngFor="let pageNum of pageNumbers" class="page-number"
          [ngClass]="{'active': pageNum === currentPage, 'ellipsis': pageNum === -1}"
          (click)="pageNum !== -1 && goToPage(pageNum)">
          {{pageNum === -1 ? '...' : pageNum}}
        </span>
      </div>

      <ion-button fill="clear" class="pagination-btn" [disabled]="currentPage === totalPages" (click)="nextPage()">
        <ion-icon name="chevron-forward" slot="icon-only"></ion-icon>
      </ion-button>
    </div>

    <!-- Results Info -->
    <div class="results-info" *ngIf="filteredQuotations.length > 0">
      <p class="results-text">
        Showing {{((currentPage - 1) * itemsPerPage) + 1}} to
        {{Math.min(currentPage * itemsPerPage, filteredQuotations.length)}}
        of {{filteredQuotations.length}} quotations
      </p>
    </div>
  </ion-content>
</div>