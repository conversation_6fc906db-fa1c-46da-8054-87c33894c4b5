import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { NavController } from '@ionic/angular';

@Component({
  selector: 'app-client-quotation-basic-info',
  templateUrl: './client-quotation-basic-info.component.html',
  styleUrls: ['./client-quotation-basic-info.component.scss'],
  standalone: false
})
export class ClientQuotationBasicInfoComponent implements OnInit, OnDestroy {

  quotationData = {
    refId: '15486524',
    contactPersonName: '',
    email: '',
    phoneNumber: '',
    bookingSummary: ''
  };

  onClickValidation = false;
  isKeyboardOpen = false;

  constructor(private readonly navController: NavController) { }

  ngOnInit() {
    this.setupKeyboardListeners();
  }

  ngOnDestroy() {
    this.removeKeyboardListeners();
  }

  // Setup simple keyboard handling for web/mobile
  private setupKeyboardListeners() {
    // Listen for focus events on input elements
    document.addEventListener('focusin', (event) => {
      const target = event.target as HTMLElement;
      if (target && (target.tagName === 'ION-INPUT' || target.tagName === 'ION-TEXTAREA' || target.tagName === 'INPUT' || target.tagName === 'TEXTAREA')) {
        this.isKeyboardOpen = true;
        // Set keyboard height for mobile simulation
        document.body.style.setProperty('--keyboard-height', '300px');
      }
    });

    document.addEventListener('focusout', () => {
      setTimeout(() => {
        // Check if any input is still focused
        const activeElement = document.activeElement;
        if (!activeElement || (activeElement.tagName !== 'ION-INPUT' && activeElement.tagName !== 'ION-TEXTAREA' && activeElement.tagName !== 'INPUT' && activeElement.tagName !== 'TEXTAREA')) {
          this.isKeyboardOpen = false;
          document.body.style.setProperty('--keyboard-height', '0px');
        }
      }, 100);
    });
  }

  // Remove keyboard event listeners
  private removeKeyboardListeners() {
    // Clean up event listeners
    document.removeEventListener('focusin', this.setupKeyboardListeners);
    document.removeEventListener('focusout', this.setupKeyboardListeners);
  }

  // Navigate back to quotation management
  goBack() {
    this.navController.navigateBack('/client-portal/quotation');
  }

  // Cancel and go back to quotation list
  cancel() {
    this.navController.navigateBack('/client-portal/quotation');
  }

  // Submit the form
  async submitQuotation(form: any): Promise<void> {
    this.onClickValidation = true;

    if (!form.valid) {
      return;
    }

    // Here you would typically save the quotation data

    // Navigate back to quotation management screen
    this.navController.navigateBack('/client-portal/quotation');
  }
}
