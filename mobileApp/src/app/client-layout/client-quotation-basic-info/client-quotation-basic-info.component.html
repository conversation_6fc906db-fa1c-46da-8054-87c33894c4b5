<div class="quotation-basic-info-page">
  <!-- Custom Header with curved design -->
  <div class="quotation-header-section">
    <div class="header-content">
      <ion-button fill="clear" class="back-button" (click)="goBack()">
        <ion-icon name="arrow-back" slot="icon-only"></ion-icon>
      </ion-button>
      <span class="header-title">Basic Information</span>
    </div>
    <div class="curved-bottom"></div>
  </div>

  <ion-content class="quotation-basic-info-body-section" [scrollEvents]="true" [forceOverscroll]="false">
    <div class="form-title">
      <span class="info-text">Enter information</span>
    </div>

    <form #quotationForm="ngForm" class="custom-form">
      <!-- Ref ID -->
      <div class="form-field">
        <div class="input-container">
          <ion-item class="custom-input-item" lines="none">
            <ion-input label="Ref. ID" labelPlacement="floating" name="refId" [(ngModel)]="quotationData.refId"
              readonly="true" mode="md">
            </ion-input>
          </ion-item>
        </div>
      </div>

      <!-- Contact Person Name -->
      <div class="form-field">
        <div class="input-container">
          <ion-item class="custom-input-item" lines="none"
            [ngClass]="{ 'is-invalid': contactPersonName.invalid && onClickValidation }">
            <ion-input label="Contact Person Name" labelPlacement="floating" name="contactPersonName" required
              [(ngModel)]="quotationData.contactPersonName" #contactPersonName="ngModel" maxlength="100"
              pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md">
            </ion-input>
          </ion-item>
        </div>
        <app-validation-message [field]="contactPersonName" [onClickValidation]="onClickValidation"
          [customPatternMessage]="'Only alphabetic characters are allowed.'">
        </app-validation-message>
      </div>

      <!-- Email -->
      <div class="form-field">
        <div class="input-container">
          <ion-item class="custom-input-item" lines="none"
            [ngClass]="{ 'is-invalid': email.invalid && onClickValidation }">
            <ion-input label="Email" labelPlacement="floating" name="email" type="email" required
              pattern="^[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,3}$" [(ngModel)]="quotationData.email" #email="ngModel">
            </ion-input>
          </ion-item>
        </div>
        <app-validation-message [field]="email" [onClickValidation]="onClickValidation"
          [customPatternMessage]="'Please provide a valid email address.'">
        </app-validation-message>
      </div>

      <!-- Phone Number -->
      <div class="form-field">
        <div class="input-container phone-input">
          <ion-item class="custom-input-item" lines="none"
            [ngClass]="{'is-invalid': phoneNumber.invalid && onClickValidation}">
            <div class="phone-flag" slot="start">
              <span class="flag-text">🇨🇦</span>
            </div>
            <ion-input label="Phone Number" labelPlacement="floating" name="phoneNumber" type="tel" required
              [(ngModel)]="quotationData.phoneNumber" #phoneNumber="ngModel" placeholder="(*************">
            </ion-input>
          </ion-item>
        </div>
        <app-validation-message [field]="phoneNumber" [onClickValidation]="onClickValidation"
          [customPatternMessage]="'Please provide a valid contact number.'">
        </app-validation-message>
      </div>

      <!-- Booking Summary -->
      <div class="form-field">
        <div class="input-container textarea-container">
          <ion-item class="custom-input-item" lines="none">
            <ion-textarea label="Booking Summary" labelPlacement="floating" name="bookingSummary"
              [(ngModel)]="quotationData.bookingSummary" rows="4" placeholder="Enter booking summary..." mode="md">
            </ion-textarea>
          </ion-item>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <ion-button class="cancel-btn" expand="full" fill="clear" type="button" (click)="cancel()">
          Cancel
        </ion-button>
        <ion-button class="submit-btn" expand="full" shape="round" type="submit" [disabled]="!quotationForm.valid"
          (click)="submitQuotation(quotationForm)">
          Submit
        </ion-button>
      </div>
    </form>
  </ion-content>
</div>