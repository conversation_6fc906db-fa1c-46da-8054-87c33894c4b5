.quotation-basic-info-page {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;

    // Custom Header Section
    .quotation-header-section {
        position: relative;
        background: #FFEA00;
        height: calc(120px + env(safe-area-inset-top, 0px));
        display: flex;
        align-items: center;
        padding-top: env(safe-area-inset-top, 0px);
        flex-shrink: 0;
        z-index: 10;

        .header-content {
            display: flex;
            align-items: center;
            width: 100%;
            padding: 0 20px;
            z-index: 2;

            .back-button {
                --color: #000;
                --padding-start: 0;
                --padding-end: 8px;
                margin: 0;

                ion-icon {
                    font-size: 24px;
                    color: #000;
                }
            }

            .header-title {
                font-size: 18px;
                font-weight: 600;
                color: #000;
                margin-left: 8px;
            }
        }

        .curved-bottom {
            position: absolute;
            bottom: -30px;
            left: 0;
            right: 0;
            height: 30px;
            background: #FFEA00;
            border-radius: 0 0 50% 50%;
            z-index: 1;
        }
    }

    .quotation-basic-info-body-section {
        flex: 1;
        --background: #f8f9fa;
        --padding-top: 40px;
        --padding-bottom: 20px;
        --padding-start: 20px;
        --padding-end: 20px;

        // Responsive adjustments for smaller screens
        @media (max-height: 600px) {
            --padding-top: 20px;
            --padding-bottom: 10px;
        }

        .form-title {
            margin-bottom: 30px;

            .info-text {
                font-size: 16px;
                font-weight: 600;
                color: #333;
            }

            // Reduce margin when keyboard is open
            @media (max-height: 600px) {
                margin-bottom: 20px;
            }
        }

        .custom-form {
            .form-field {
                margin-bottom: 20px;

                // Reduce margin when keyboard is open
                @media (max-height: 600px) {
                    margin-bottom: 15px;
                }

                .input-container {
                    background: #ffffff;
                    border-radius: 12px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
                    overflow: hidden;

                    .custom-input-item {
                        --background: transparent;
                        --border-radius: 12px;
                        --padding-start: 16px;
                        --padding-end: 16px;
                        --min-height: 56px;

                        ion-input,
                        ion-textarea {
                            --color: #333;
                            --placeholder-color: #999;
                            font-size: 16px;

                            &.ion-focused {
                                --color: #333;
                            }

                            // Floating label styling
                            .label-text {
                                color: #666;
                                font-size: 14px;
                            }

                            &.has-focus .label-text {
                                color: #FFEA00;
                            }
                        }
                    }

                    &.phone-input {
                        .custom-input-item {
                            .phone-flag {
                                display: flex;
                                align-items: center;
                                margin-right: 12px;
                                min-width: 32px;

                                .flag-text {
                                    font-size: 18px;
                                }
                            }
                        }
                    }

                    &.textarea-container {
                        .custom-input-item {
                            --min-height: 120px;
                            align-items: flex-start;
                            padding-top: 16px;
                            padding-bottom: 16px;

                            ion-textarea {
                                min-height: 80px;
                            }
                        }
                    }

                    &.is-invalid {
                        border: 1px solid #dc3545;
                        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.15);
                    }

                    // Focus state
                    &:focus-within {
                        box-shadow: 0 2px 12px rgba(255, 234, 0, 0.2);
                        border: 1px solid #FFEA00;
                    }
                }

                // Validation message styling
                app-validation-message {
                    margin-top: 8px;
                    display: block;
                }
            }

            .action-buttons {
                display: flex;
                gap: 12px;
                margin-top: 40px;
                padding: 20px 0;
                position: sticky;
                bottom: 0;
                background: #f8f9fa;
                border-top: 1px solid #e0e0e0;
                margin-left: -20px;
                margin-right: -20px;
                padding-left: 20px;
                padding-right: 20px;
                z-index: 10;

                // Adjust for keyboard when open
                @media (max-height: 600px) {
                    margin-top: 20px;
                    padding: 15px 20px;
                }

                .cancel-btn {
                    --color: #666;
                    --background: transparent;
                    font-size: 16px;
                    font-weight: 500;
                    height: 48px;
                    flex: 1;
                }

                .submit-btn {
                    --background: #000;
                    --color: #fff;
                    font-size: 16px;
                    font-weight: 600;
                    height: 48px;
                    flex: 1;
                    --border-radius: 24px;

                    &:disabled {
                        --background: #ccc;
                        --color: #666;
                    }
                }
            }
        }
    }
}