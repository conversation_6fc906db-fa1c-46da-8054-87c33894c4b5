import { ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { NavController, Platform } from '@ionic/angular';
import { filter, Subscription } from 'rxjs';
import { CommonService } from 'src/services/common.service';


@Component({
  selector: 'app-client-layout',
  templateUrl: './client-layout.component.html',
  styleUrls: ['./client-layout.component.scss'],
  standalone: false
})
export class ClientLayoutComponent implements OnInit, OnDestroy {

  showTabs = true;
  private routeSub!: Subscription;

  constructor(private readonly navController: NavController,
    private readonly router: Router,
    private readonly platform: Platform,
    public readonly commonService: CommonService,
  ) {

  }

  ngOnInit() {
    this.platform.ready().then(() => {
      if (this.platform.is('cordova')) {
        //  this.fcmService.initializeFirebaseToken();
      }
    });
    this.routeSub = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.checkRoute();
      });
  }

  ionViewWillEnter() {
  }

  private checkRoute() {
    const currentRoute = this.router.url;

    const hideTabRoutes = [
      '/client-portal/dashboard',
      '/client-portal/shipping',
      '/client-portal/quotation',
      '/client-portal/calendar',
      '/client-portal/account',
      '/client-portal/basic/info',
      '/client-portal/other/info',
      '/client-portal/pickup/delivery',
      '/client-portal/delivery/location',
      '/client-portal/cargo/listing',
      '/client-portal/add/cargo/details',
      '/client-portal/special/request',
      '/client-portal/quotation/basic-info'
    ];

    this.showTabs = hideTabRoutes.some(route => currentRoute.startsWith(route));

    // Set active tab manually
    if (currentRoute.startsWith('/client-portal/basic/info') || currentRoute.startsWith('/client-portal/other/info') ||
      currentRoute.startsWith('/client-portal/pickup/delivery') || currentRoute.startsWith('/client-portal/delivery/location')
      || currentRoute.startsWith('/client-portal/cargo/listing') || currentRoute.startsWith('/client-portal/add/cargo/details')
      || currentRoute.startsWith('/client-portal/special/request')) {
      this.commonService.activeTab = 'shipping';
    } else if (currentRoute.startsWith('/client-portal/shipping')) {
      this.commonService.activeTab = 'shipping';
    } else if (currentRoute.startsWith('/client-portal/quotation')) {
      this.commonService.activeTab = 'quotation';
    } else if (currentRoute.startsWith('/client-portal/calendar')) {
      this.commonService.activeTab = 'calendar';
    } else if (currentRoute.startsWith('/client-portal/account')) {
      this.commonService.activeTab = 'account';
    } else {
      this.commonService.activeTab = '';
    }
  }

  ngOnDestroy() {
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }
  }

}
