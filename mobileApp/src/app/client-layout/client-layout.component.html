<ion-router-outlet></ion-router-outlet>
<ion-tabs class="driver-bees-navigation" [ngClass]="{'hide-tab-bar':!showTabs}">
  <ion-tab-bar class="tabs" slot="bottom">
    <ion-tab-button tab="dashboard">
      <div class="icon-container-bees">
        <ion-icon class="non-selected-icon" [src]="'/assets/images/svg/tab-home.svg'"></ion-icon>
        <ion-icon class="selected-icon" [src]="'/assets/images/svg/home-icon.svg'"></ion-icon>
      </div>
      <ion-label>Home</ion-label>
    </ion-tab-button>
    <!-- <ion-tab-button tab="shipping">
      <div class="icon-container-bees">
        <ion-icon class="non-selected-icon" [src]="'/assets/images/svg/shipping-icon.svg'"></ion-icon>
        <ion-icon class="selected-icon" [src]="'/assets/images/svg/select-shipping-icon.svg'"></ion-icon>
      </div>
      <ion-label>Shipping</ion-label>
    </ion-tab-button> -->
    <ion-tab-button tab="shipping">
      <div class="icon-container-bees">
        <ion-icon *ngIf="commonService.activeTab !== 'shipping'"
          [src]="'/assets/images/svg/shipping-icon.svg'"></ion-icon>
        <ion-icon *ngIf="commonService.activeTab === 'shipping'"
          [src]="'/assets/images/svg/select-shipping-icon.svg'"></ion-icon>
      </div>
      <ion-label [class.active-label]="commonService.activeTab === 'shipping'">Shipping</ion-label>
    </ion-tab-button>

    <ion-tab-button tab="quotation">
      <div class="icon-container-bees">
        <ion-icon *ngIf="commonService.activeTab !== 'quotation'" [src]="'/assets/images/svg/fuel-icon.svg'"></ion-icon>
        <ion-icon *ngIf="commonService.activeTab === 'quotation'"
          [src]="'/assets/images/svg/select-fuel-icon.svg'"></ion-icon>
      </div>
      <ion-label [class.active-label]="commonService.activeTab === 'quotation'">Quotation</ion-label>
    </ion-tab-button>

    <ion-tab-button tab="calendar">
      <div class="icon-container-bees">
        <ion-icon class="non-selected-icon" [src]="'/assets/images/svg/calendar.svg'"></ion-icon>
        <ion-icon class="selected-icon" [src]="'/assets/images/svg/select-calendar-icon.svg'"></ion-icon>
      </div>
      <ion-label>Calendar</ion-label>
    </ion-tab-button>

    <ion-tab-button tab="account">
      <div class="icon-container-bees">
        <ion-icon class="non-selected-icon" [src]="'/assets/images/svg/setting-icon.svg'"></ion-icon>
        <ion-icon class="selected-icon" [src]="'/assets/images/svg/select-setting-icon.svg'"></ion-icon>
      </div>
      <ion-label>Setting</ion-label>
    </ion-tab-button>
  </ion-tab-bar>
</ion-tabs>