import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { CommonService } from 'src/services/common.service';
import { UserProfileService } from 'src/services/user-profile.service';
import { LoadingService } from 'src/services/loading.service';
import { ToastService } from 'src/shared/toast.service';
import { UserProfileModel, UserProfileResponse } from 'src/modals/user-profile-response';
import { RestResponse } from 'src/shared/auth.model';
import mask from 'src/shared/phone-number.mask';
import { maskitoGetCountryFromNumber } from '@maskito/phone';
import metadata from 'libphonenumber-js/min/metadata';
import { isValidPhoneNumber } from 'libphonenumber-js/core';
import { NgModel } from '@angular/forms';

@Component({
  selector: 'app-client-account',
  templateUrl: './client-account.component.html',
  styleUrls: ['./client-account.component.scss'],
  standalone: false
})
export class ClientAccountComponent implements OnInit, OnDestroy {

  onClickValidation!: boolean;
  profileData: UserProfileModel = new UserProfileModel();
  activeSubscriptions: Subscription = new Subscription();
  isApple: boolean = false;
  code: string = "";
  protected readonly mask = mask;

  constructor(
    private readonly navController: NavController,
    private readonly userProfileService: UserProfileService,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    public commonService: CommonService,
  ) {

  }

  ngOnInit(): void {
    this.resetForm();
  }

  ionViewWillEnter(): void {
    // Always load fresh profile data when screen is entered
    this.loadUserProfile();
  }

  resetForm(): void {
    this.profileData = new UserProfileModel();
    this.onClickValidation = false;
  }

  // Load user profile from API (always fresh data)
  loadUserProfile(): void {
    this.loadingService.show();

    this.activeSubscriptions.add(
      this.userProfileService.refreshUserProfile().subscribe({
        next: (response: UserProfileResponse) => {
          this.loadingService.hide();
          if (response.status === 200 && response.data) {
            this.profileData = UserProfileModel.fromApiResponse(response.data);
          } else {
            this.toastService.show('Failed to load profile data');
          }
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message || 'Failed to load profile');
        }
      })
    );
  }

  protected get countryIsoCode(): string {
    const phone = this.profileData?.phoneNumber;
    if (!phone || phone.trim() === '') {
      return '';
    }

    const code = maskitoGetCountryFromNumber(phone, metadata) ?? '';
    return code ? `/assets/images/icons/flags/${code.toLowerCase()}.png` : '';
  }

  protected get pattern(): string {
    return this.isApple ? '+[0-9-]{1,20}' : '';
  }

  onPhoneChange(userPhone: NgModel): void {
    setTimeout(() => {
      const phone = this.profileData?.phoneNumber?.trim() || '';

      const currentErrors = userPhone.control.errors || {};

      if (phone === '') {
        userPhone.control.setErrors({ required: true });
      } else {
        const isValid = isValidPhoneNumber(phone, metadata);

        if (isValid) {
          delete currentErrors['pattern'];
          delete currentErrors['required'];
          userPhone.control.setErrors(Object.keys(currentErrors).length ? currentErrors : null);
        } else {
          userPhone.control.setErrors({ pattern: true });
        }
      }
    });
  }

  async submitProfile(form: any): Promise<void> {
    this.onClickValidation = true;

    if (!form.valid || !this.profileData.isValid(form)) {
      return;
    }

    this.loadingService.show();

    this.activeSubscriptions.add(
      this.userProfileService.updateUserProfile(this.profileData).subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          if (response.status === 200) {
            this.toastService.show('Profile updated successfully!');
            // Profile data is automatically updated in global state by the service
            this.navController.navigateForward("/client-portal/dashboard", { animated: true });
          } else {
            this.toastService.show(response.message || 'Failed to update profile');
          }
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message || 'Failed to update profile');
        }
      })
    );
  }

  cancel() {
    this.navController.navigateRoot("/client-portal/dashboard", { animated: true });
  }

  goBack() {
    this.navController.back();
  }

  ngOnDestroy(): void {
    this.activeSubscriptions.unsubscribe();
  }

}
