.profile-page {
    --background: #f8f9fa;
    height: 100%;
    --padding-top: 0;
    --offset-top: 0;

    // Custom Header Section
    .profile-header-section {
        position: relative;
        background: #FFEA00;
        height: 120px;
        display: flex;
        align-items: center;
        padding-top: env(safe-area-inset-top, 0px);

        .header-content {
            display: flex;
            align-items: center;
            width: 100%;
            padding: 0 20px;
            z-index: 2;

            .back-button {
                --color: #000;
                --padding-start: 0;
                --padding-end: 8px;
                margin: 0;

                ion-icon {
                    font-size: 24px;
                    color: #000;
                }
            }

            .header-title {
                font-size: 18px;
                font-weight: 600;
                color: #000;
                margin-left: 8px;
            }
        }

        .curved-bottom {
            position: absolute;
            bottom: -30px;
            left: 0;
            right: 0;
            height: 30px;
            background: #FFEA00;
            border-radius: 0 0 50% 50%;
            z-index: 1;
        }
    }

    .profile-page-body-section {
        padding: 40px 20px 20px 20px;
        background: #f8f9fa;
        min-height: calc(100vh - 120px);

        .form-title {
            margin-bottom: 30px;

            .profile-text {
                font-size: 16px;
                font-weight: 600;
                color: #333;
            }
        }

        .form-container {
            .custom-form {
                .form-field {
                    margin-bottom: 20px;

                    .input-container {
                        background: #ffffff;
                        border-radius: 12px;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
                        overflow: hidden;

                        .custom-input-item {
                            --background: transparent;
                            --border-radius: 12px;
                            --padding-start: 16px;
                            --padding-end: 16px;
                            --min-height: 56px;

                            ion-input {
                                --color: #333;
                                --placeholder-color: #999;
                                font-size: 16px;

                                &.ion-focused {
                                    --color: #333;
                                }

                                // Floating label styling
                                .label-text {
                                    color: #666;
                                    font-size: 14px;
                                }

                                &.has-focus .label-text {
                                    color: #FFEA00;
                                }
                            }

                            .custom-mobile-input {
                                border: none;
                                outline: none;
                                background: transparent;
                                font-size: 16px;
                                color: #333;
                                width: 100%;
                                padding: 16px 0;

                                &::placeholder {
                                    color: #999;
                                }
                            }
                        }

                        &.phone-input {
                            .custom-input-item {
                                .phone-flag {
                                    display: flex;
                                    align-items: center;
                                    margin-right: 12px;
                                    min-width: 32px;

                                    img {
                                        width: 24px;
                                        height: 24px;
                                        border-radius: 50%;
                                        object-fit: cover;
                                    }

                                    .flag-text {
                                        font-size: 18px;
                                        display: none; // Hide emoji, use image instead
                                    }
                                }
                            }
                        }

                        &.is-invalid {
                            border: 1px solid #dc3545;
                            box-shadow: 0 2px 8px rgba(220, 53, 69, 0.15);
                        }

                        // Focus state
                        &:focus-within {
                            box-shadow: 0 2px 12px rgba(255, 234, 0, 0.2);
                            border: 1px solid #FFEA00;
                        }
                    }

                    // Validation message styling
                    app-validation-message {
                        margin-top: 8px;
                        display: block;
                    }
                }
            }

            .action-buttons {
                display: flex;
                gap: 12px;
                margin-top: 40px;
                padding-bottom: 20px;

                .cancel-btn {
                    --color: #666;
                    --background: transparent;
                    font-size: 16px;
                    font-weight: 500;
                    height: 48px;
                    flex: 1;
                }

                .next-btn {
                    --background: #000;
                    --color: #fff;
                    font-size: 16px;
                    font-weight: 600;
                    height: 48px;
                    flex: 1;
                    --border-radius: 24px;

                    &:disabled {
                        --background: #ccc;
                        --color: #666;
                    }
                }
            }
        }
    }

    // Disabled field styling
    .disabled-field {
        opacity: 0.6;

        ion-input {
            --color: #666 !important;
            --placeholder-color: #999 !important;
            cursor: not-allowed;
        }
    }
}