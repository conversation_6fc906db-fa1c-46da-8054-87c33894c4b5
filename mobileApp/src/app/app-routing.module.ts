import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';
import { LoginComponent } from './authentication/login/login.component';
import { OnboardingPage } from './onboarding/onboarding.page';
import { ForgotPasswordComponent } from './authentication/forgot-password/forgot-password.component';
import { ResetPasswordComponent } from './authentication/reset-password/reset-password.component';
import { DriverEmailComponent } from './driver-layout/profile-management/driver-onboarding/driver-email/driver-email.component';
import { DriverPasswordComponent } from './driver-layout/profile-management/driver-onboarding/driver-password/driver-password.component';
import { DriverBasicInfoComponent } from './driver-layout/profile-management/driver-onboarding/driver-basic-info/driver-basic-info.component';
import { DriverCompanyInfoComponent } from './driver-layout/profile-management/driver-onboarding/driver-company-info/driver-company-info.component';
import { DriverAddressComponent } from './driver-layout/profile-management/driver-onboarding/driver-address/driver-address.component';
import { AuthGuard } from 'src/shared/authguard';
import { OtpForgotPasswordComponent } from './authentication/otp-forgot-password/otp-forgot-password.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'account/onboarding',
    pathMatch: 'full'
  },
  {
    path: 'account/onboarding',
    component: OnboardingPage,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_ANONYMOUS'] }
  },
  {
    path: "account/register",
    component: DriverEmailComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_ANONYMOUS'] }
  },
  {
    path: "account/register/password",
    component: DriverPasswordComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_ANONYMOUS'] }
  },
  {
    path: "account/register/basic/info",
    component: DriverBasicInfoComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_ANONYMOUS'] }
  },
  {
    path: "account/register/company/info",
    component: DriverCompanyInfoComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_ANONYMOUS'] }
  },
  {
    path: "account/register/address",
    component: DriverAddressComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_ANONYMOUS'] }
  },
  {
    path: 'account/login',
    component: LoginComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_ANONYMOUS'] },
  },
  {
    path: 'account/forgot/password',
    component: ForgotPasswordComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_ANONYMOUS'] },
  },
  {
    path: 'account/reset/otp',
    component: OtpForgotPasswordComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_ANONYMOUS'] },
  },
  {
    path: 'account/reset/password',
    component: ResetPasswordComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_ANONYMOUS'] },
  },
  {
    path: 'portal',
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_DRIVER', 'ROLE_OFFICE_ADMIN'] },
    loadChildren: () => import('./driver-layout/driver-layout.module').then(m => m.DriverLayoutModule)
  },
  {
    path: 'client-portal',
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_CUSTOMER'] },
    loadChildren: () => import('./client-layout/client-layout.module').then(m => m.ClientLayoutModule)
  },
];
@NgModule({
  imports: [
    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })
  ],
  exports: [RouterModule]
})
export class AppRoutingModule { }
