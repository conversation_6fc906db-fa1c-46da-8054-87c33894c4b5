.login-page {
    z-index: 999999999;
    // Remove top padding/margin for iOS safe area
    --padding-top: 0;
    --offset-top: 0;

    .custom-form {
        height: 100%;
    }

    .login-header-section {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        // Extend to top of screen including safe area
        padding-top: env(safe-area-inset-top, 0px);
        margin-top: calc(-1 * env(safe-area-inset-top, 0px));

        .back-button {
            position: absolute;
            top: 40px;
            left: 10px;

            ion-icon {
                font-size: 30px;
                color: #000; // You can adjust this color
            }
        }

        .bees-logo-image {
            position: absolute;
            top: 35%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }

    .login-page-container {
        z-index: 999999999;
        padding: 0px 20px 0px;
        text-align: center;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        background-color: var(--ion-color-primary-contrast);
        min-height: 0%;

        .login-container {
            display: flex;
            flex-direction: column;
            gap: 5px;
            text-align: center;

            .page-heading {
                font-size: 22px;
                color: var(--ion-color-warning-contrast);
                font-weight: 400;
                margin-top: 10px;
            }

            .page-heading-title {
                font-size: 14px;
            }
        }

        .lock-container {
            display: flex;
            align-items: center;
            justify-content: center;

            .lock-icon {
                font-size: 40px;
                border: 1px solid #D4D4D4;
                border-radius: 18px;
                padding: 12px;
            }
        }

        .form-container {
            min-height: 250px;

            .forgot-password {
                font-size: 14px;
                letter-spacing: 0;
                color: black;
                font-weight: 600;
                text-align: right;
                margin-right: 5px;
            }

            ion-button {
                --background: #FFEA00;
                --box-shadow: 0px 10px 20px rgba(58, 255, 223, 0.31);
                min-height: 54px;
                --border-radius: 22px;
                text-transform: uppercase;
                color: black;
                font-weight: 600;
                font-size: unset;
            }
        }

        .register-container {
            text-align: center;
            margin-top: 75px;

            p {
                font-size: 14px;
                color: var(--ion-color-primary);
                font-weight: 600;
            }

            a {
                color: #250ED0;
                font-weight: 600;
                text-decoration: underline;
            }
        }

    }
}