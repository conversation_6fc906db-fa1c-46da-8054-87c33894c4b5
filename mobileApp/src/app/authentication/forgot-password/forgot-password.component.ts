import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { IonInput, NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { ToastService } from 'src/shared/toast.service';
import mask from './../../../shared/phone-number.mask';
import { ActivatedRoute } from '@angular/router';
import { AuthenticationModel } from 'src/modals/authentication-model';
import { StatusBarService } from 'src/services/status-bar.service';
import { Style } from '@capacitor/status-bar';
@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss'],
  standalone: false
})
export class ForgotPasswordComponent implements <PERSON>I<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> {

  onClickValidation!: boolean; // Flag to control validation state
  forgotPasswordData: AuthenticationModel = new AuthenticationModel(); // Login data object
  subscription: Subscription = new Subscription(); // Subscription to manage observable lifecycle
  view: string = "";
  protected readonly mask = mask;

  constructor(private readonly navController: NavController,
    private readonly dataService: DataService,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    private statusBarService: StatusBarService,
    private readonly route: ActivatedRoute
  ) {

  }

  ngOnInit(): void {
    this.init();
  }

  ionViewWillEnter() {
    // Use predefined color scheme
    this.statusBarService.setColorScheme('authentication');

    // Or set custom colors
    this.statusBarService.setCustomStatusBar({
      backgroundColor: '#FDFBDF', // Orange
      style: Style.Light // Light text on dark background
    });
  }

  init() {
    this.forgotPasswordData = new AuthenticationModel();
    this.onClickValidation = false;
  }

  async forgotPassword(form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid || !this.forgotPasswordData.isValidForgotPasswordRequest(form)) {
      return; // Exit if form is invalid
    }
    this.loadingService.show();
    this.subscription = this.dataService.forgotPassword(this.forgotPasswordData)
      .subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          const data = response.data;
          this.redirectToResetOtp(data);
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message);
        }
      })
  }

  redirectToResetOtp(data: any) {
    this.navController.navigateForward("/account/reset/otp", {
      queryParams: {
        forgotData: data
      }, animated: true
    });
  }

  goToLoginPage() {
    this.navController.navigateRoot("/account/login", { animated: true });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
