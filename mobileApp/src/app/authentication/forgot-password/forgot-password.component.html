<ion-content class="forgot-password-page">

  <div class="forgot-password-header-section">
    <ion-buttons slot="start" class="back-button">
      <ion-button (click)="goToLoginPage()" fill="clear">
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <img src="/assets/images/icons/header-image.png" />
    <img class="bees-logo-image" src="/assets/images/icons/bees-logo.png" />
  </div>

  <div class="forgot-password-page-container">

    <div class="forgot-password-container margin-bottom-10">
      <ion-icon class="forgot-password-icon" src="assets/images/svg/forgot-pass-icon.svg" slot="start"></ion-icon>
    </div>

    <div class="forgot-password-container">
      <span class="page-heading">Forgot<strong> Password</strong></span>
      <span class="forgot-heading-title">Enter your email or Username and we'll send you a link to
        get
        back into your account</span>
    </div>

    <div class="form-container">
      <form class="custom-form" #forgotPassForm="ngForm" novalidate (ngSubmit)="forgotPassword(forgotPassForm.form)">

        <div class="margin-top-30">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':userEmail.invalid && onClickValidation}">
            <ion-icon src="/assets/images/svg/email-icon.svg" slot="start" class="start-icon"></ion-icon>
            <ion-input #forgotEmailInput label="Email Address" labelPlacement="floating" required="required"
              name="userEmail" #userEmail="ngModel" pattern="^[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,3}$"
              [(ngModel)]="forgotPasswordData.email"
              [ngClass]="{'is-invalid':userEmail.invalid && onClickValidation}"></ion-input>
          </ion-item>
          <app-validation-message [field]="userEmail" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid email address.'">
          </app-validation-message>
        </div>

        <!-- Submit -->
        <div>
          <ion-button class="margin-top-20" expand="full" shape="round" type="submit">
            Generate Link
          </ion-button>
        </div>
      </form>
    </div>

  </div>
</ion-content>