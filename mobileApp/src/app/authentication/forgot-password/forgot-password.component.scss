.forgot-password-page {
    z-index: 999999999;
    // Remove top padding/margin for iOS safe area
    --padding-top: 0;
    --offset-top: 0;

    .custom-form {
        height: 100%;
    }

    .forgot-password-header-section {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        // Extend to top of screen including safe area
        padding-top: env(safe-area-inset-top, 0px);
        margin-top: calc(-1 * env(safe-area-inset-top, 0px));

        .back-button {
            position: absolute;
            top: 40px;
            left: 10px;

            ion-icon {
                font-size: 30px;
                color: #000; // You can adjust this color
            }
        }

        .bees-logo-image {
            position: absolute;
            top: 35%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }

    .forgot-password-page-container {
        z-index: 999999999;
        padding: 0px 20px 0px;
        text-align: center;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        background-color: var(--ion-color-primary-contrast);
        min-height: 0%;

        .forgot-password-container {
            display: flex;
            flex-direction: column;
            gap: 5px;
            text-align: center;

            .page-heading {
                font-size: 22px;
                color: var(--ion-color-warning-contrast);
                font-weight: 400;
                margin-top: 10px;
            }

            .forgot-heading-title {
                font-size: 12px;
            }
        }

        .forgot-password-container {
            display: flex;
            align-items: center;
            justify-content: center;

            .forgot-password-icon {
                font-size: 80px;
                border-radius: 65px;
                padding: 25px;
                background: #FFEA00;
            }
        }

        .form-container {
            min-height: 250px;

            ion-button {
                --background: black;
                min-height: 54px;
                --border-radius: 18px;
                text-transform: uppercase;
                color: white;
                font-weight: 400;
                font-size: 12px;
            }
        }

    }
}