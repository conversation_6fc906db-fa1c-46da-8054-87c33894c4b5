.otp-forgot-password-page {
    z-index: 999999999;

    .custom-form {
        height: 100%;
    }

    .otp-forgot-password-header-section {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;

        .back-button {
            position: absolute;
            top: 40px;
            left: 10px;

            ion-icon {
                font-size: 30px;
                color: #000; // You can adjust this color
            }
        }

        .bees-logo-image {
            position: absolute;
            top: 35%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }

    .otp-forgot-password-page-container {
        z-index: 999999999;
        padding: 0px 20px 0px;
        text-align: center;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        background-color: var(--ion-color-primary-contrast);
        min-height: 0%;

        .otp-forgot-password-container {
            display: flex;
            flex-direction: column;
            gap: 5px;
            text-align: center;

            .page-heading {
                font-size: 22px;
                color: var(--ion-color-warning-contrast);
                font-weight: 400;
                margin-top: 10px;
            }

            .forgot-heading-title {
                font-size: 12px;
            }
        }

        .otp-forgot-password-container {
            display: flex;
            align-items: center;
            justify-content: center;

            .otp-forgot-password-icon {
                font-size: 80px;
                border-radius: 65px;
                padding: 25px;
                background: #FFEA00;
            }
        }

        .form-container {
            min-height: 250px;

            .otp-validate-container {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin: 0px auto;
                margin-top: 10px;

                &.disabled {
                    pointer-events: none;
                    opacity: 0.5;
                }

                ion-input {
                    display: inline-block;
                    max-width: 50px;
                    height: 50px;
                    --padding-start: 8px;
                    border: 2px solid var(--ion-color-input-border);
                    border-radius: 8px;
                    text-align: center;
                    --padding-top: 15px;
                    --padding-bottom: 15px;
                    --padding-end: 8px;

                    .input-wrapper {
                        .input-highlight {
                            height: unset;
                        }
                    }

                    &.is-invalid {
                        border: 2px solid var(--ion-color-warning-dark-red) !important;
                    }
                }
            }

            ion-button {
                --background: black;
                min-height: 54px;
                --border-radius: 18px;
                text-transform: uppercase;
                color: white;
                font-weight: 400;
                font-size: 12px;
            }
        }

        .resend-otp-text {
            color: #29385b;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;

            .green-text {
                color: var(--ion-color-green-text) !important;
            }
        }

    }
}