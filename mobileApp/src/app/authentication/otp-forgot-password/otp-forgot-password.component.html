<ion-content class="otp-forgot-password-page">
    <div class="otp-forgot-password-header-section">
        <ion-buttons slot="start" class="back-button">
            <ion-button (click)="goToLoginPage()" fill="clear">
                <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
            </ion-button>
        </ion-buttons>
        <img src="/assets/images/icons/header-image.png" />
        <img class="bees-logo-image" src="/assets/images/icons/bees-logo.png" />
    </div>

    <div class="otp-forgot-password-page-container">
        <div class="otp-forgot-password-container margin-bottom-10">
            <ion-icon class="otp-forgot-password-icon" src="\assets\images\svg\forgot-pass-icon.svg"
                slot="start"></ion-icon>
        </div>
        <div class="otp-forgot-password-container">
            <span class="page-heading">Forgot<strong> Password</strong></span>
            <span class="forgot-heading-title">Enter the 6-digit OTP below.</span>
        </div>

        <div class="form-container">
            <form #otpForgotPasswordForm="ngForm" novalidate="novalidate" class="custom-form" autocomplete="off"
                (ngSubmit)="continue(otpForgotPasswordForm.form)">
                <div class="margin-bottom-100 margin-top-20">
                    <div class="otp-validate-container"
                        [ngClass]="{'active': !responseReceived, 'disabled': responseReceived }">
                        <ion-input required="required" minlength="1" maxlength="1" inputmode="numeric" type="text"
                            placeholder="*" [debounce]="100" name="otpInput1"
                            [ngClass]="{ 'is-invalid': onClickValidation && otpInput1.invalid }"
                            [(ngModel)]="data.otpInput1" #otpInput1="ngModel"
                            (ionInput)="commonService.changeInputFocus($event)"
                            (keydown)="commonService.handleBackspace($event)">
                        </ion-input>
                        <ion-input required="required" inputmode="numeric" type="text" placeholder="*" [debounce]="100"
                            name="otpInput2" #otpInput2="ngModel" [(ngModel)]="data.otpInput2"
                            [ngClass]="{ 'is-invalid': onClickValidation && otpInput2.invalid }" minlength="1"
                            maxlength="1" (ionInput)="commonService.changeInputFocus($event)"
                            (keydown)="commonService.handleBackspace($event)">
                        </ion-input>
                        <ion-input required="required" inputmode="numeric" type="text" placeholder="*" name="otpInput3"
                            [debounce]="100" #otpInput3="ngModel" [(ngModel)]="data.otpInput3"
                            [ngClass]="{ 'is-invalid': onClickValidation && otpInput3.invalid}" minlength="1"
                            maxlength="1" (ionInput)="commonService.changeInputFocus($event)"
                            (keydown)="commonService.handleBackspace($event)">
                        </ion-input>
                        <ion-input required="required" inputmode="numeric" type="text" placeholder="*" name="otpInput4"
                            [debounce]="100" #otpInput4="ngModel" [(ngModel)]="data.otpInput4"
                            [ngClass]="{ 'is-invalid': onClickValidation && otpInput4.invalid }" minlength="1"
                            maxlength="1" (ionInput)="commonService.changeInputFocus($event)"
                            (keydown)="commonService.handleBackspace($event)">
                        </ion-input>
                        <ion-input required="required" inputmode="numeric" type="text" placeholder="*" name="otpInput5"
                            [debounce]="100" #otpInput5="ngModel" [(ngModel)]="data.otpInput5"
                            [ngClass]="{ 'is-invalid': onClickValidation && otpInput5.invalid}" minlength="1"
                            maxlength="1" (ionInput)="commonService.changeInputFocus($event)"
                            (keydown)="commonService.handleBackspace($event)">
                        </ion-input>
                        <ion-input required="required" inputmode="numeric" type="text" placeholder="*" name="otpInput6"
                            [debounce]="100" #otpInput6="ngModel" [(ngModel)]="data.otpInput6"
                            [ngClass]="{ 'is-invalid': onClickValidation && otpInput6.invalid}" minlength="1"
                            maxlength="1" (ionInput)="commonService.changeInputFocus($event)"
                            (keydown)="commonService.handleBackspace($event)">
                        </ion-input>
                    </div>
                </div>
                <!-- Submit button for the OTP form -->
                <ion-button class="margin-top-20" expand="full" shape="round" type="submit">
                    Continue
                </ion-button>
            </form>
            <div class="ion-text-center margin-bottom-10 margin-top-20 resend-otp-text">
                <div *ngIf="counter>0 && !responseReceived">Resend enable in {{counter}} seconds</div>
                <a (click)="resendOtp()" *ngIf="counter<=0  && !responseReceived">Resend</a>
                <a class="green-text" *ngIf="responseReceived">Otp Verified!</a>
            </div>
        </div>

    </div>
</ion-content>