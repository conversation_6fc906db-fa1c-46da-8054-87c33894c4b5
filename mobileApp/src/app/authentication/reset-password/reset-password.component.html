<ion-content class="reset-password-page">

  <div class="reset-password-header-section">
    <ion-buttons slot="start" class="back-button">
      <ion-button (click)="goToLoginPage()" fill="clear">
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <img src="/assets/images/icons/header-image.png" />
    <img class="bees-logo-image" src="/assets/images/icons/bees-logo.png" />
  </div>

  <div class="reset-password-page-container">

    <div class="reset-password-container margin-bottom-10">
      <ion-icon class="reset-password-icon" src="\assets\images\svg\forgot-pass-icon.svg" slot="start"></ion-icon>
    </div>

    <div class="reset-password-container">
      <span class="page-heading">Reset<strong> Password</strong></span>
    </div>

    <!-- Container for the Reset Password form -->
    <div class="form-container">
      <form #resetPasswordForm="ngForm" novalidate="novalidate" class="custom-form" autocomplete="off"
        (ngSubmit)="resetPassword(resetPasswordForm.form)">

        <div class="margin-top-30">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':!newPasswords.valid && onClickValidation}">
            <ion-input label="Enter New Password" labelPlacement="floating" mode="md" [type]="passwordFieldType"
              name="newPasswords" pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}"
              [ngClass]="{'is-invalid':newPasswords.invalid && onClickValidation}" #newPasswords="ngModel"
              [(ngModel)]="data.password" required="required"></ion-input>
            <i-feather name="eye" (click)="eyePassword()"
              *ngIf="data.password && data.password.length>0 && passwordFieldType==='password'"></i-feather>
            <i-feather name="eye-off" (click)="eyePassword()"
              *ngIf="data.password && data.password.length>0 && passwordFieldType!=='password'"></i-feather>
          </ion-item>
          <app-validation-message [field]="newPasswords" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Password must contain a capital letter, number and special character & should be greater than 8 characters'">
          </app-validation-message>
        </div>

        <div class="margin-top-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':!confirmNewPassword.valid && onClickValidation}">
            <ion-input label="Confirm New Password" labelPlacement="floating" mode="md" [type]="passwordFieldType"
              name="confirmNewPassword" pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}"
              [ngClass]="{'is-invalid':confirmNewPassword.invalid && onClickValidation}" #confirmNewPassword="ngModel"
              [(ngModel)]="data.confirmPassword" required="required"></ion-input>
            <i-feather name="eye" (click)="eyePassword()"
              *ngIf="data.confirmPassword && data.confirmPassword.length>0 && passwordFieldType==='password'"></i-feather>
            <i-feather name="eye-off" (click)="eyePassword()"
              *ngIf="data.confirmPassword && data.confirmPassword.length>0 && passwordFieldType!=='password'"></i-feather>
          </ion-item>
          <app-validation-message [field]="confirmNewPassword" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Password must contain a capital letter, number and special character & should be greater than 8 characters'">
          </app-validation-message>
        </div>

        <!-- Submit button for the Reset Password form -->
        <ion-button class="margin-top-20" expand="full" shape="round" type="submit">
          Update Password
        </ion-button>
      </form>
    </div>

  </div>
</ion-content>