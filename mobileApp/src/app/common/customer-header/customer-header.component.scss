.common-header-section {
    //  position: fixed;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 135px;

    .header-bg {
        height: 140px;
        width: 100%;
    }

    .header-content {
        position: absolute;
        top: 40px;
        left: 0;
        width: 100%;
        padding: 0 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        z-index: 2;
    }

    .header-icon {
        //  background-color: #fff;
        width: 43px;
        height: 43px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .back-icon {
            width: 28px;
            height: 28px;
            object-fit: cover;
        }

        .back-icon {
            stroke: #000; // Optional styling
        }
    }

    .header-title {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        font-weight: bold;
        font-size: 14px;
        color: #000;
        text-align: center;
        //  white-space: nowrap;
    }

    .right-icon-container {
        display: flex;
        align-items: center;
        justify-content: center;

        .right-icon {
            background: white;
            font-size: 17px;
            border: 1px solid black;
            border-radius: 25px;
            padding: 11px;
        }
    }
}