import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { NavController } from '@ionic/angular';
import { AuthService } from 'src/shared/authservice';
import { DataService } from 'src/services/data.service'; // Add this import for notification service
import { ToastService } from 'src/shared/toast.service'; // Handle errors
import { EventService } from 'src/shared/event.service';
import { LocalStorageService } from 'src/shared/local-storage.service';

@Component({
  selector: 'app-customer-header',
  templateUrl: './customer-header.component.html',
  styleUrls: ['./customer-header.component.scss'],
  standalone: false
})
export class CustomerHeaderComponent implements OnInit {
  @Input() innerPage: boolean = false;
  @Input() headingText: string = "";
  @Input() rightAction: boolean = false;
  @Input() rightActionIcon: string = "";
  @Input() showBackIcon: boolean = true;

  @Output() rightActionCallback: EventEmitter<any> = new EventEmitter<any>();

  @Input() backUrl: string | null = null;
  @Input() profileImageUrl: string | null = null;

  constructor(
    private navController: NavController
  ) { }

  ngOnInit() {

  }

  ionViewWillEnter() {

  }

  ngOnDestroy() {
  }

  back() {
    if (this.backUrl) {
      this.navController.navigateBack(this.backUrl);
    } else {
      this.navController.back({ animated: true });
    }
  }

  onRightActionClick() {
    this.rightActionCallback.emit(null);
  }

}
