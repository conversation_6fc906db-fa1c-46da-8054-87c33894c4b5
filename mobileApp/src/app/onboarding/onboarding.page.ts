import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { NavController } from '@ionic/angular';
@Component({
  selector: 'app-onboarding',
  templateUrl: './onboarding.page.html',
  styleUrls: ['./onboarding.page.scss'],
  standalone: false
})
export class OnboardingPage implements OnInit, OnDestroy {

  // Slide button properties
  slidePosition: number = 0;
  isSliding: boolean = false;
  isCompleted: boolean = false;
  isDragging: boolean = false;
  startX: number = 0;
  maxSlideDistance: number = 0;
  slideThreshold: number = 0.7; // 70% of the track width

  // Event listener references for cleanup
  private slideButton: HTMLElement | null = null;

  constructor(private readonly navController: NavController) {
  }

  ngOnInit() {
    // Reset slide button state
    this.resetSlideButton();

    // Calculate max slide distance after view init
    setTimeout(() => {
      this.calculateMaxSlideDistance();
      this.setupEventListeners();
    }, 100);
  }

  ionViewWillEnter() {
    // Reset slide button state every time user enters this page
    this.resetSlideButton();

    // Recalculate max slide distance
    setTimeout(() => {
      this.calculateMaxSlideDistance();
    }, 100);
  }

  ionViewWillLeave() {
    // Clean up any ongoing slide operations when leaving the page
    this.resetSlideButton();
  }

  ngOnDestroy() {
    // Clean up event listeners
    this.resetSlideButton();
  }

  // Setup event listeners with proper options
  setupEventListeners() {
    this.slideButton = document.querySelector('.slide-button') as HTMLElement;
    // Event listeners are handled through Angular template binding
    // This method is kept for future enhancements if needed
  }

  goToRegister() {
    console.log('Get Started button clicked - navigating to registration');
    this.navController.navigateForward("/account/register", { animated: true });
  }

  goToLogin() {
    console.log('Sign in button clicked - navigating to login');
    this.navController.navigateForward("/account/login", { animated: true });
  }

  onSlideConfirm() {
    console.log('Sign in confirmed!');
    // Navigate or perform action here
  }

  // Reset slide button to initial state
  resetSlideButton() {
    this.slidePosition = 0;
    this.isSliding = false;
    this.isCompleted = false;
    this.isDragging = false;
    this.startX = 0;
    this.maxSlideDistance = 0;
  }

  // Calculate the maximum slide distance
  calculateMaxSlideDistance() {
    const container = document.querySelector('.slide-track') as HTMLElement;
    const button = document.querySelector('.slide-button') as HTMLElement;
    if (container && button) {
      this.maxSlideDistance = container.offsetWidth - button.offsetWidth - 8; // 8px for padding
    }
  }

  // Touch events for mobile
  onTouchStart(event: TouchEvent) {
    if (this.isCompleted) return;

    // Check if the touch is on the slide button element
    const target = event.target as HTMLElement;
    const isSlideButton = target.closest('.slide-button');

    if (isSlideButton && event.cancelable) {
      event.preventDefault();
      event.stopPropagation();
    }

    this.isDragging = true;
    this.isSliding = true;
    this.startX = event.touches[0].clientX - this.slidePosition;
  }

  onTouchMove(event: TouchEvent) {
    if (!this.isDragging || this.isCompleted) return;

    // Only prevent default if we're actively dragging and event is cancelable
    if (event.cancelable) {
      event.preventDefault();
      event.stopPropagation();
    }

    const currentX = event.touches[0].clientX - this.startX;
    this.updateSlidePosition(currentX);
  }

  onTouchEnd(event: TouchEvent) {
    if (!this.isDragging || this.isCompleted) return;

    // Only prevent default if event is cancelable
    if (event.cancelable) {
      event.preventDefault();
      event.stopPropagation();
    }

    this.handleSlideEnd();
  }

  // Mouse events for desktop
  onMouseDown(event: MouseEvent) {
    if (this.isCompleted) return;

    // Only prevent default if the event is cancelable
    if (event.cancelable) {
      event.preventDefault();
    }

    this.isDragging = true;
    this.isSliding = true;
    this.startX = event.clientX - this.slidePosition;
  }

  onMouseMove(event: MouseEvent) {
    if (!this.isDragging || this.isCompleted) return;

    // Only prevent default if the event is cancelable
    if (event.cancelable) {
      event.preventDefault();
    }

    const currentX = event.clientX - this.startX;
    this.updateSlidePosition(currentX);
  }

  onMouseUp(event: MouseEvent) {
    if (!this.isDragging || this.isCompleted) return;

    // Only prevent default if the event is cancelable
    if (event.cancelable) {
      event.preventDefault();
    }

    this.handleSlideEnd();
  }

  // Update slide position
  updateSlidePosition(newPosition: number) {
    this.slidePosition = Math.max(0, Math.min(newPosition, this.maxSlideDistance));
  }

  // Handle slide end
  handleSlideEnd() {
    this.isDragging = false;
    this.isSliding = false;

    // Check if slide is completed (reached threshold)
    if (this.slidePosition >= this.maxSlideDistance * this.slideThreshold) {
      this.completeSlide();
    } else {
      // Reset to start position with animation
      this.slidePosition = 0;
    }
  }

  // Complete the slide action
  completeSlide() {
    this.isCompleted = true;
    this.slidePosition = this.maxSlideDistance;

    // Wait for animation to complete, then navigate
    setTimeout(() => {
      this.goToRegister();
    }, 800);
  }

}

