// Slide to unlock container
.slide-to-unlock-container {
    width: 100%;
    margin: 0 auto;
    position: relative;
    z-index: 10;
    touch-action: pan-y; // Allow vertical scrolling but prevent horizontal
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
}

.slide-track {
    background: #000;
    border-radius: 10px;
    height: 60px;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    touch-action: pan-y; // Allow vertical scrolling but prevent horizontal
}

.slide-text {
    color: #fff;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    transition: opacity 0.3s ease;
    z-index: 1;
    position: absolute;
    left: 70px;
    /* Position text to the right of the button */
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);

    &.hidden {
        opacity: 0;
    }

    .slide-arrows {
        display: flex;
        align-items: center;
        gap: 4px;

        ion-icon {
            font-size: 18px;
            color: #FFEA00;
            animation: slideArrows 2s infinite;

            &.arrow-2 {
                animation-delay: 0.2s;
            }

            &.arrow-3 {
                animation-delay: 0.4s;
            }
        }
    }
}

.success-text {
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: 1;
    position: absolute;
    left: 70px;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);

    &.show {
        opacity: 1;
    }

    .success-icon {
        font-size: 24px;
        color: #4CAF50;
    }
}

.slide-button {
    position: absolute;
    left: 4px;
    top: 4px;
    width: 52px;
    height: 52px;
    background: #FFEA00;
    border-radius: 20%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: grab;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 2;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    will-change: transform;
    touch-action: none; // Prevent all default touch behaviors on the button
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;

    &:active {
        cursor: grabbing;
        transform: scale(1.05);
    }

    &.sliding {
        transition: none;
        transform: scale(1.05);
    }

    &.completed {
        background: #4CAF50;
        transform: scale(1.1);
        box-shadow: 0 4px 16px rgba(76, 175, 80, 0.4);
    }

    // Reset animation
    &.resetting {
        transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .slide-icon {
        display: flex;
        align-items: center;
        justify-content: center;

        ion-icon {
            font-size: 24px;
            color: #000;
            transition: all 0.3s ease;
        }

        .check-icon {
            color: #fff;
            animation: checkBounce 0.6s ease;
        }
    }
}

// Animations
@keyframes slideArrows {

    0%,
    100% {
        opacity: 0.4;
        transform: translateX(0);
    }

    50% {
        opacity: 1;
        transform: translateX(4px);
    }
}

@keyframes checkBounce {
    0% {
        transform: scale(0);
    }

    50% {
        transform: scale(1.3);
    }

    100% {
        transform: scale(1);
    }
}


.onboarding-screen {
    --background: yello;
    background: url('assets/images/background.png') no-repeat center center / cover;

}

.content-overlay {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 20px;
    background: rgba(0, 0, 0, 0.1);
    //  padding-bottom: env(safe-area-inset-bottom, 20px);
}

.bottom-content {
    color: #fff;
    text-align: left;
}

.bottom-content h2 {
    margin-bottom: 10px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 18px;
    color: black;
    width: 65%;
}

.bottom-content p {
    font-family: 'Montserrat', sans-serif;
    font-weight: 200;
    font-size: 12px;
    color: black;
    opacity: 0.8;
    width: 85%;
}

ion-button {
    --background: #000;
    --color: #fff;
    font-family: 'Montserrat', sans-serif;
    font-weight: 200;
    font-size: 13px;
    color: black;
    margin-bottom: 20px;
    opacity: 0.8;
}

.register-section {
    font-size: 14px;
    color: black;
    margin-top: 10px;
    text-align: center;
    cursor: pointer;
    position: relative;
    z-index: 10;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    padding: 10px;
}

// Responsive design for slide button
@media (max-width: 480px) {
    .slide-track {
        height: 55px;
    }

    .slide-button {
        width: 47px;
        height: 47px;
    }

    .slide-text {
        font-size: 14px;
        left: 60px;
        /* Adjust for smaller button */

        .slide-arrows ion-icon {
            font-size: 16px;
        }
    }

    .success-text {
        font-size: 14px;
        left: 60px;
        /* Adjust for smaller button */

        .success-icon {
            font-size: 20px;
        }
    }
}

@media (max-width: 360px) {
    .slide-track {
        height: 50px;
    }

    .slide-button {
        width: 42px;
        height: 42px;
    }

    .slide-text {
        font-size: 13px;
        left: 55px;
        /* Adjust for smallest button */
    }

    .success-text {
        font-size: 13px;
        left: 55px;
        /* Adjust for smallest button */
    }
}