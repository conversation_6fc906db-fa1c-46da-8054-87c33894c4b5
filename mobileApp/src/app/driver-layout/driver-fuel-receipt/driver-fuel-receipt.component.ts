import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController, ActionSheetController, AlertController, ModalController } from '@ionic/angular';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { ToastService } from 'src/shared/toast.service';
import { LoadingService } from 'src/services/loading.service';
import { FullImageComponent } from 'src/shared/full-image/full-image.component';
import { FileCropperComponent } from 'src/shared/file-cropper/file-cropper.component';

@Component({
  selector: 'app-driver-fuel-receipt',
  templateUrl: './driver-fuel-receipt.component.html',
  styleUrls: ['./driver-fuel-receipt.component.scss'],
  standalone: false
})
export class DriverFuelReceiptComponent implements OnInit {

  kmsReading: string = '';
  fuelInLiter: string = '';
  fuelCost: string = '';
  onClickValidation!: boolean;

  formData: any;
  showValidationErrors = false;
  requestImage: string | null = null;
  uploadedImages: { url: string, selected?: boolean }[] = [
    { url: 'assets/images/icons/image1.png' },
    { url: 'assets/images/icons/image2.png' },
    { url: 'assets/images/icons/image1.png' }
  ];

  constructor(
    private route: ActivatedRoute,
    private navController: NavController,
    private modalCtrl: ModalController,
    private toastService: ToastService,
    private loadingService: LoadingService,
    private changeDetectorRef: ChangeDetectorRef,
  ) { }

  ngOnInit() {
    this.formData = {};
  }

  goBack() {
    this.navController.back();
  }

  async upload() {
    try {
      const response = await Camera.getPhoto({
        quality: 50,
        allowEditing: false,
        resultType: CameraResultType.Base64,
        source: CameraSource.Prompt, // Prompt user to select from camera or files
      });
      if (response.base64String) {
        await this.processCropper(response.base64String);
      }
    } catch (error) {
      //  this.toastService.show("Something went wrong while uploading profile picture.");
    }
  }

  async processCropper(base64Image: string) {
    const modal = await this.modalCtrl.create({
      component: FileCropperComponent,
      componentProps: {
        file: { base64String: base64Image }
      },
      cssClass: "cropper-modal"
    });
    await modal.present();
    const { data, role } = await modal.onWillDismiss();
    if (role !== 'confirm') {
      this.toastService.show("Sorry, profile picture uploading has been cancelled.");
      return;
    }
    if (data && data.croppedFile) {
      await this.uploadImage(data.croppedFile);
    } else {
      this.toastService.show("No file selected after cropping.");
    }
  }

  async uploadImage(blob: Blob): Promise<void> {
    if (!blob) {
      this.toastService.show('No file selected. Please choose an image to upload.');
      return;
    }

    const file = new File([blob], 'cropped-image.png', { type: 'image/png' }); // Convert Blob to File

    const formData = new FormData();
    formData.append('file', file, file.name);

    this.loadingService.show(); // Show loading indicator

    // this.dataService.uploadFile(formData).subscribe({
    //   next: (response: any) => {
    //     this.loadingService.hide();
    //     this.handleUploadResponse(response);
    //   },
    //   error: (error) => {
    //     this.loadingService.hide();
    //     this.toastService.show(error.message || 'An error occurred while uploading the file');
    //   }
    // });
  }

  private handleUploadResponse(response: any): void {
    if (Array.isArray(response) && response.length > 0) {
      response.forEach((attachment: any) => {
        const fileUrl = attachment.path || attachment.fileName;
        this.uploadedImages.push({ url: fileUrl });
      });
      this.changeDetectorRef.detectChanges();
      this.toastService.show('File uploaded successfully.');
    } else {
      this.toastService.show('Failed to upload file.');
    }
  }

  toggleSelected(selectedImage: { url: string, selected?: boolean }) {
    this.uploadedImages.forEach(image => {
      if (image === selectedImage) {
        image.selected = !image.selected; // Toggle current image
      } else {
        image.selected = false; // Deselect others
      }
    });
  }

  onDeleteClick(event: Event, image: { url: string }) {
    event.stopPropagation(); // Prevent parent (click) from firing
    this.removeImage(image);
  }

  removeImage(image: { url: string }) {
    this.uploadedImages = this.uploadedImages.filter(img => img.url !== image.url);
  }

  async viewImage(imageUrl: string) {
    const modal = await this.modalCtrl.create({
      component: FullImageComponent,
      componentProps: { imageUrl },
      cssClass: 'full-image-modal'
    });
    await modal.present();
    const { data, role } = await modal.onWillDismiss();
    if (role === 'remove' && data) {
      this.removeImage({ url: data }); // use existing method
    }
  }

  async submitProfile(form: any): Promise<void> {
    if (!this.requestImage) {
      this.toastService.show('Please upload at least one image before submitting.');
      return;
    }

    const payload = {
      attachments: this.generateAttachments()
    };
    this.navController.navigateForward('/portal/shipping', { animated: true });
  }

  private generateAttachments() {
    if (!this.requestImage) return [];

    const images = Array.isArray(this.requestImage) ? this.requestImage : [this.requestImage];

    return images.map((url, index) => ({
      fileName: `image-${index + 1}`, // Keep the filename simple
      mimeType: 'image/*', // Use a wildcard to accept any image type
      path: url,
      originalName: `image-${index + 1}`
    }));
  }

}
