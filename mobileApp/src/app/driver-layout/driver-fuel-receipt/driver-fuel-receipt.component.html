<ion-content class="fuel-receipt-page">
  <!-- Header matching parent design -->
  <app-customer-header [innerPage]="true" [headingText]="'Fuel Receipt'" [rightAction]="false"></app-customer-header>

  <div class="fuel-receipt-body-section">
    <div class="margin-top-25">
      <span class="info-text">Fuel Receipt</span>
    </div>
    <div class="form-container">
      <form class="custom-form" #basicForm="ngForm" novalidate>

        <!-- Kms Reading -->
        <div class="margin-top-10 margin-bottom-10">
          <ion-item class="site-form-control" lines="none">
            <ion-input label="Kms Reading" labelPlacement="floating" name="kmsReading" [(ngModel)]="kmsReading"
              type="number" pattern="[0-9]*" inputmode="numeric" required>
            </ion-input>
          </ion-item>
          <app-validation-message [field]="kmsReading" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please add valid Kms Reading (numbers only).'">
          </app-validation-message>
        </div>

        <!-- Fuel in Liters -->
        <div class="margin-top-10 margin-bottom-10">
          <ion-item class="site-form-control" lines="none">
            <ion-input label="Fuel in Liters" labelPlacement="floating" name="fuelInLiter" [(ngModel)]="fuelInLiter"
              type="number" pattern="[0-9]*" inputmode="numeric" required>
            </ion-input>
          </ion-item>
          <app-validation-message [field]="fuelInLiter" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please add valid Fuel in Liters (numbers only).'">
          </app-validation-message>
        </div>
        <!-- Fuel Cost -->
        <div class="margin-top-10 margin-bottom-10">
          <ion-item class="site-form-control" lines="none">
            <ion-input label="Fuel Cost" labelPlacement="floating" name="fuelCost" [(ngModel)]="fuelCost" type="number"
              pattern="^\d*\.?\d*$" inputmode="decimal" required>
            </ion-input>
          </ion-item>
          <app-validation-message [field]="fuelCost" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please add valid fuel cost (numbers and decimal only).'">
          </app-validation-message>
        </div>

        <!-- Image Upload Section -->
        <div class="document-upload-container margin-top-20"
          [ngClass]="{'missing-image': showValidationErrors && !requestImage}">
          <img *ngIf="!requestImage" src="assets/images/svg/attach.svg" alt="Upload Icon" class="upload-icon">
          <div class="upload-text" *ngIf="!requestImage">
            <span class="choose-file-text">Upload a picture of your item</span>
            <span class="upload-multiple-file-text">upload multiple or single file upload (max. 2mb)</span>
            <ion-button class="browse-file-button margin-top-20" expand="full" shape="round" (click)="upload()">
              Browse file
            </ion-button>
          </div>
        </div>

        <!-- Image Gallery Below -->
        <div class="image-grid margin-top-30">
          <div class="image-item" *ngFor="let image of uploadedImages" (click)="toggleSelected(image)"
            [class.selected]="image.selected">
            <img [src]="image.url" alt="Uploaded" />
            <div class="icon-container" *ngIf="image.selected">
              <ion-icon class="icon" src="assets/images/svg/view-icon.svg" (click)="viewImage(image.url)"></ion-icon>
              <ion-icon class="icon" src="assets/images/svg/delete-icon.svg"
                (click)="onDeleteClick($event, image)"></ion-icon>
            </div>
          </div>
        </div>

        <!-- <div class="upload-section">
        <div class="upload-container" (click)="selectImage()">
          <div class="upload-icon">
            <ion-icon name="camera" size="large"></ion-icon>
          </div>
          <p>Upload a picture of your item</p>
          <p class="upload-subtitle">Click here to browse file</p>
        </div>

        <ion-button expand="block" fill="solid" color="warning" (click)="selectImage()" class="browse-button">
          Browse file
        </ion-button>
      </div> -->

        <div class="fuel-receipt-btn-container">
          <ion-button class="margin-top-20 site-button ship-cancel-btn" expand="full" shape="round" (click)="goBack()">
            <span>Cancel</span>
          </ion-button>
          <ion-button class="margin-top-20 site-button ship-submit-btn" expand="full" shape="round"
            (click)="submitProfile(basicForm)" [disabled]="!basicForm.valid">
            <span>Submit</span>
          </ion-button>
        </div>

      </form>

    </div>

  </div>
</ion-content>