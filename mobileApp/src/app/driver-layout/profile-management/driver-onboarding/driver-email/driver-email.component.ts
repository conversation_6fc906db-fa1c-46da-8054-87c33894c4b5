import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { ProfileDetail } from 'src/modals/profileDetail';
import { DataService } from 'src/services/data.service';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';
import { CommonService } from 'src/services/common.service';
import mask from './../../../../../shared/phone-number.mask';
import { maskitoGetCountryFromNumber } from '@maskito/phone';
import metadata from 'libphonenumber-js/min/metadata';
import { isValidPhoneNumber } from 'libphonenumber-js/core';
import { NgModel } from '@angular/forms';
import { StatusBarService } from 'src/services/status-bar.service';
import { Style } from '@capacitor/status-bar';

@Component({
  selector: 'app-driver-email',
  templateUrl: './driver-email.component.html',
  styleUrls: ['./driver-email.component.scss'],
  standalone: false
})
export class DriverEmailComponent implements OnInit {

  onClickValidation!: boolean;
  profile: ProfileDetail = new ProfileDetail();
  passwordFieldType: string = 'password';
  confirmPassword: string | null = null;
  isApple: boolean = false;
  code: string = "";
  protected readonly mask = mask;

  // Phone number display properties
  displayPhoneNumber: string = '';
  showPhonePrefix: boolean = false;

  constructor(private readonly navController: NavController,
    private readonly localStorageService: LocalStorageService,
    private readonly dataService: DataService,
    private statusBarService: StatusBarService,
    private readonly toastService: ToastService
  ) {

  }
  ionViewWillEnter() {
    // Use predefined color scheme
    this.statusBarService.setColorScheme('authentication');
    // Or set custom colors
    this.statusBarService.setCustomStatusBar({
      backgroundColor: '#FDFBDF', // Orange
      style: Style.Light // Light text on dark background
    });
  }
  ngOnInit() {
    this.onClickValidation = false;
    // Clear old data when page opens
    this.clearFormData();
  }

  ionViewDidEnter() {
    // Clear old data when page opens
    this.clearFormData();
  }

  private clearFormData() {
    // Clear the stored onboarding data
    this.localStorageService.remove("CUSTOMER_ONBOARDING");
    // Reset profile to new instance
    this.profile = new ProfileDetail();
    // Reset phone display properties
    this.displayPhoneNumber = '';
    this.showPhonePrefix = false;
    this.confirmPassword = null;
  }

  goToLogin() {
    this.localStorageService.remove("CUSTOMER_ONBOARDING");
    this.navController.navigateRoot("/account/login", { animated: true });
  }

  validatePasswords(): boolean {
    if (this.profile.password !== this.confirmPassword) {
      this.toastService.show('Password and Confirm Password do not match');
      return false;
    }
    return true;
  }

  eyePassword() {
    if (this.passwordFieldType === "password") {
      this.passwordFieldType = "text";
    } else {
      this.passwordFieldType = "password";
    }
  }

  protected get countryIsoCode(): string {
    const phone = this.profile?.phoneNumber;
    if (!phone || phone.trim() === '') {
      return '';
    }

    const code = maskitoGetCountryFromNumber(phone, metadata) ?? '';
    //  return code ? `/assets/images/icons/flags/${code.toLowerCase()}.png` : '';
    return code ? `/assets/images/icons/flags/ca.png` : '';
  }

  protected get pattern(): string {
    return '^\\+1\\d{10}$'; // +1 followed by exactly 10 digits
  }

  formatPhoneNumber(event: any, userPhone: NgModel): void {
    const input = event.target;
    let value: string = input.value || '';

    // Remove all non-digit characters
    const digits = value.replace(/\D/g, '').slice(0, 10); // Max 10 digits

    // Update display properties
    this.showPhonePrefix = digits.length > 0;

    let formatted = digits;
    if (digits.length > 6) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3, 6)} ${digits.slice(6)}`;
    } else if (digits.length > 3) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3)}`;
    }

    // Update display phone number (without +1 prefix)
    this.displayPhoneNumber = formatted;

    // Store the full phone number with +1 prefix for API (only if digits exist)
    this.profile.phoneNumber = digits.length > 0 ? `+1${digits}` : '';

    // Update input value to show formatted number without prefix
    input.value = formatted;

    // Validation
    if (digits.length > 0 && digits.length < 10) {
      userPhone.control.setErrors({ required: true });
    } else {
      userPhone.control.setErrors(null);
    }
  }

  onPhoneFocus(): void {
    // Optional: Add any focus behavior if needed
  }

  onPhoneBlur(): void {
    // Optional: Add any blur behavior if needed
  }

  async continue(form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid || !this.validatePasswords()) {
      return;
    }
    this.localStorageService.setObject("CUSTOMER_ONBOARDING", this.profile);
    this.navController.navigateForward("/account/register/address", { animated: true });
  }

}
