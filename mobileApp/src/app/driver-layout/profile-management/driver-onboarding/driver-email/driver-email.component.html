<ion-content class="onboarding-page">

  <div class="onboarding-header-section">
    <img src="/assets/images/icons/header-image.png" />
    <img class="bees-logo-image" src="/assets/images/icons/bees-logo.png" />
  </div>

  <div class="onboarding-page-container">

    <div class="lock-container">
      <ion-icon class="lock-icon" src="/assets\images\svg\lock-icon.svg" slot="start"></ion-icon>
    </div>

    <div class="onboarding-container">
      <p class="page-heading-title">Enter your details to create an<strong> account</strong>
      </p>
    </div>

    <!-- Login Form -->
    <div class="form-container">
      <form class="custom-form" #registerForm="ngForm" novalidate (ngSubmit)="continue(registerForm.form)">

        <div class="common-fields-container">
          <div class="field-container small-field">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': firstName.invalid && onClickValidation}">
              <ion-input name="firstName" #firstName="ngModel" [(ngModel)]="profile.firstName" required="required"
                maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="First Name" labelPlacement="floating">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="firstName" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </div>

          <div class="field-container large-field">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': lastName.invalid && onClickValidation}">
              <ion-input name="lastName" #lastName="ngModel" [(ngModel)]="profile.lastName" required="required"
                maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="Last Name" labelPlacement="floating">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="lastName" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </div>
        </div>

        <!-- Phone Number -->
        <!-- <div class="margin-top-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':userPhone.invalid && onClickValidation}">
            <img width="30" height="30" [attr.alt]="countryIsoCode" [src]="countryIsoCode" slot="start"
              [style.border-radius.%]="50" *ngIf="countryIsoCode!==''" />
            <input class="custom-mobile-input" inputmode="tel" [attr.pattern]="pattern" [maskito]="mask"
              required="required" name="userPhone" #userPhone="ngModel" [(ngModel)]="profile.phoneNumber"
              placeholder="Phone Number"
              (ngModelChange)="profile.phoneNumber = $event.trim(); onPhoneChange(userPhone)" />
          </ion-item>
          <app-validation-message [field]="userPhone" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid contact number.'">
          </app-validation-message>
        </div> -->

        <!-- Phone Number -->
        <div class="margin-top-10">
          <ion-item class="site-form-control phone-input-container" lines="none"
            [ngClass]="{'is-invalid':userPhone.invalid && onClickValidation}">
            <ion-icon src="/assets/images/svg/canada-flag-icon.svg" slot="start" class="start-icon"></ion-icon>
            <div class="phone-input-wrapper">
              <span class="phone-prefix" *ngIf="showPhonePrefix">+1</span>
              <ion-input
                inputmode="tel"
                required
                name="userPhone"
                #userPhone="ngModel"
                [(ngModel)]="displayPhoneNumber"
                placeholder="Phone Number"
                (ionInput)="formatPhoneNumber($event, userPhone)"
                (ionFocus)="onPhoneFocus()"
                (ionBlur)="onPhoneBlur()">
              </ion-input>
            </div>
          </ion-item>
          <app-validation-message [field]="userPhone" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid contact number.'">
          </app-validation-message>
        </div>

        <div class="margin-top-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':username.invalid && onClickValidation}">
            <ion-icon src="/assets/images/svg/email-icon.svg" slot="start" class="start-icon"></ion-icon>
            <ion-input #emailInput label="Email Id" labelPlacement="floating" required name="username"
              #username="ngModel" pattern="^[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,3}$" [(ngModel)]="profile.email">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="username" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid email address.'">
          </app-validation-message>
        </div>

        <div class="margin-top-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':userPassword.invalid && onClickValidation}">
            <ion-icon src="/assets/images/svg/password-icon.svg" slot="start" class="start-icon"></ion-icon>
            <ion-input mode="md" label="Password" labelPlacement="floating" [type]="passwordFieldType"
              name="userPassword" #userPassword="ngModel" [(ngModel)]="profile.password" required
              pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}">
            </ion-input>
            <i-feather name="eye" (click)="eyePassword()"
              *ngIf="profile.password?.length && passwordFieldType !== 'password'"></i-feather>
            <i-feather name="eye-off" (click)="eyePassword()"
              *ngIf="profile.password?.length && passwordFieldType === 'password'"></i-feather>
          </ion-item>
          <app-validation-message [field]="userPassword" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Password must contain a capital letter, number and special character & should be greater than 8 characters'">
          </app-validation-message>
        </div>

        <div class="margin-top-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':!confirmNewPassword.valid && onClickValidation}">
            <ion-icon src="/assets/images/svg/password-icon.svg" slot="start" class="start-icon"></ion-icon>
            <ion-input label="Confirm New Password" labelPlacement="floating" mode="md" [type]="passwordFieldType"
              name="confirmNewPassword" pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}"
              [ngClass]="{'is-invalid':confirmNewPassword.invalid && onClickValidation}" #confirmNewPassword="ngModel"
              [(ngModel)]="confirmPassword" required="required"></ion-input>
            <i-feather name="eye" (click)="eyePassword()"
              *ngIf="confirmPassword && confirmPassword.length > 0 && passwordFieldType==='password'"></i-feather>
            <i-feather name="eye-off" (click)="eyePassword()"
              *ngIf="confirmPassword && confirmPassword.length > 0 && passwordFieldType!=='password'"></i-feather>
          </ion-item>
          <app-validation-message [field]="confirmNewPassword" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Password must contain a capital letter, number and special character & should be greater than 8 characters'">
          </app-validation-message>
        </div>

        <!-- Submit -->
        <ion-button class="margin-top-20 site-button" expand="full" shape="round" type="submit">
          <span>Continue</span>
        </ion-button>
      </form>
    </div>

    <div class="register-container">
      <p>
        Already have an account? <a (click)="goToLogin()">Sign In</a>
      </p>
    </div>

    <!-- Terms -->
    <div class="privacy-container">
      <p>
        By continuing you agree to our
        <a href="https://www.google.com" target="_blank" rel="noopener noreferrer" class="terms-link">Terms
          of Service</a>
        and
        <a href="https://www.google.com" target="_blank" rel="noopener noreferrer" class="privacy-link">Privacy
          Policy</a>
      </p>
    </div>

  </div>
</ion-content>