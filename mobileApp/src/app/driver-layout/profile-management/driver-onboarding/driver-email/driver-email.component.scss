// Phone Input Styling
.phone-input-container {
    .phone-input-wrapper {
        display: flex;
        align-items: center;
        width: 100%;

        .phone-prefix {
            color: #666;
            font-size: 16px;
            margin-right: 8px;
            font-weight: 500;
            min-width: 24px;
        }

        ion-input {
            flex: 1;
        }
    }
}

// Terms and Privacy Policy Links Styling
.privacy-container {
    p {
        text-align: center;
        font-size: 14px;
        color: #666;
        line-height: 1.5;

        .terms-link,
        .privacy-link {
            color: #FFEA00;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;

            &:hover {
                color: #e6d200;
                text-decoration: underline;
                transform: scale(1.05);
            }

            &:active {
                color: #ccbb00;
                transform: scale(0.98);
            }

            // Add a subtle underline animation
            &::after {
                content: '';
                position: absolute;
                bottom: -2px;
                left: 0;
                width: 0;
                height: 2px;
                background-color: #FFEA00;
                transition: width 0.3s ease;
            }

            &:hover::after {
                width: 100%;
            }

            // Ripple effect on tap
            &:focus {
                outline: none;
                box-shadow: 0 0 0 3px rgba(255, 234, 0, 0.3);
                border-radius: 4px;
            }
        }
    }
}