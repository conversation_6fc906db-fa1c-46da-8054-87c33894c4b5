import { Component, OnInit } from '@angular/core';
import { ModalController, NavController } from '@ionic/angular';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { FileCropperComponent } from 'src/shared/file-cropper/file-cropper.component';
import { ToastService } from 'src/shared/toast.service';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { ProfileDetail } from 'src/modals/profileDetail';
import { LocalStorageService } from 'src/shared/local-storage.service';

@Component({
  selector: 'app-driver-basic-info',
  templateUrl: './driver-basic-info.component.html',
  styleUrls: ['./driver-basic-info.component.scss'],
  standalone: false
})
export class DriverBasicInfoComponent implements OnInit {

  onClickValidation!: boolean;
  profile: ProfileDetail = new ProfileDetail();

  constructor(private readonly navController: NavController,
    private readonly toastService: ToastService,
    private readonly modalCtrl: ModalController,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService
  ) {

  }

  ngOnInit() {
    this.onClickValidation = false;
    const profile = this.localStorageService.getObject("CUSTOMER_ONBOARDING");
    this.profile = profile ? ProfileDetail.fromResponse(profile) : new ProfileDetail();
  }

  ionViewDidEnter() {
    const profile = this.localStorageService.getObject("CUSTOMER_ONBOARDING");
    this.profile = profile ? ProfileDetail.fromResponse(profile) : new ProfileDetail();
  }

  async upload() {
    try {
      const response = await Camera.getPhoto({
        quality: 50,
        allowEditing: false,
        resultType: CameraResultType.Base64,
        source: CameraSource.Prompt, // Prompt user to select from camera or files
      });
      if (response.base64String) {
        this.processCropper(response.base64String);
      }
    } catch (error) {
      //  this.toastService.show("Something went wrong while uploading profile picture.");
    }
  }

  async processCropper(base64Image: string) {
    const modal = await this.modalCtrl.create({
      component: FileCropperComponent,
      componentProps: {
        file: { base64String: base64Image }
      },
      cssClass: "cropper-modal"
    });
    await modal.present();
    const { data, role } = await modal.onWillDismiss();
    if (role !== 'confirm') {
      this.toastService.show("Sorry, profile picture uploading has been cancelled.");
      return;
    }
    if (!data?.croppedFile) {
      this.toastService.show("No file selected after cropping.");
      return;
    }
    await this.uploadImage(data.croppedFile);
  }

  async uploadImage(blob: Blob): Promise<void> {
    if (!blob) {
      this.toastService.show('No file selected. Please choose an image to upload.');
      return;
    }
    const file = new File([blob], 'cropped-image.png', { type: 'image/png' }); // Convert Blob to File
    const formData = new FormData();
    formData.append('file', file, file.name);
    this.loadingService.show(); // Show loading indicator
    // this.dataService.uploadFile(formData).subscribe({
    //   next: (response: any) => {
    //     this.loadingService.hide();
    //     this.handleFileUploadResponse(response); // Use the new function
    //   },
    //   error: (error) => {
    //     this.loadingService.hide();
    //     this.toastService.show(error.message || 'An error occurred while uploading the file');
    //   }
    // });
  }

  private handleFileUploadResponse(response: any): void {
    if (Array.isArray(response) && response.length > 0) {
      const fileData = response[0];
      const fileUrl = fileData.path || fileData.fileName;

      // Update profile picture URL
      //  this.profile.profileImageUrl = fileUrl;
      this.toastService.show('Profile picture uploaded successfully!');
    } else {
      this.toastService.show('Failed to get file URL');
    }
  }

  async continue(form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid || !this.profile.isValidBasicRequest(form)) {
      return;
    }
    this.localStorageService.setObject("CUSTOMER_ONBOARDING", this.profile);
    this.navController.navigateForward("/account/register/company/info", { animated: true });
  }

}
