import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { ProfileDetail } from 'src/modals/profileDetail';
import { LocalStorageService } from 'src/shared/local-storage.service';

@Component({
  selector: 'app-driver-password',
  templateUrl: './driver-password.component.html',
  styleUrls: ['./driver-password.component.scss'],
  standalone: false
})
export class DriverPasswordComponent implements OnInit, OnDestroy {

  onClickValidation: boolean = false;
  profile: ProfileDetail = new ProfileDetail();
  passwordFieldType!: string;
  subscription: Subscription = new Subscription();
  isProcessing: boolean = false;

  constructor(private readonly navController: NavController,
    private readonly localStorageService: LocalStorageService,
  ) {

  }

  ngOnInit() {
    this.onClickValidation = false;
    const profile = this.localStorageService.getObject("CUSTOMER_ONBOARDING");
    this.profile = profile ? ProfileDetail.fromResponse(profile) : new ProfileDetail();
  }

  ionViewDidEnter() {
    this.passwordFieldType = "password";
    const profile = this.localStorageService.getObject("CUSTOMER_ONBOARDING");
    this.profile = profile ? ProfileDetail.fromResponse(profile) : new ProfileDetail();
  }

  eyePassword() {
    if (this.passwordFieldType === "password") {
      this.passwordFieldType = "text";
    } else {
      this.passwordFieldType = "password";
    }
  }

  async continue(form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    this.localStorageService.setObject("CUSTOMER_ONBOARDING", this.profile);
    this.navController.navigateForward("/account/register/basic/info", { animated: true });
  }

  ngOnDestroy(): void {
    // Unsubscribe from any subscriptions to prevent memory leaks
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
