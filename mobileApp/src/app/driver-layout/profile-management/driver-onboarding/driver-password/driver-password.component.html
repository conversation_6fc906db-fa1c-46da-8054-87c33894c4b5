<ion-content class="onboarding-page">

  <div class="onboarding-header-section">
    <img src="/assets/images/icons/header-image.png" />
    <img class="bees-logo-image" src="/assets/images/icons/bees-logo.png" />
  </div>

  <div class="onboarding-page-container">

    <div class="lock-container">
      <ion-icon class="lock-icon" src="/assets\images\svg\lock-icon.svg" slot="start"></ion-icon>
    </div>

    <div class="onboarding-container">
      <p class="page-heading-title">Let's<strong> create your password</strong> for
        romanemail.com
      </p>
    </div>

    <!-- Login Form -->
    <div class="form-container">
      <form class="custom-form" #registerForm="ngForm" novalidate (ngSubmit)="continue(registerForm.form)">

        <div class="margin-top-20">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':userPassword.invalid && onClickValidation}">
            <ion-icon src="/assets/images/svg/password-icon.svg" slot="start" class="start-icon"></ion-icon>
            <ion-input mode="md" label="Password" labelPlacement="floating" [type]="passwordFieldType"
              name="userPassword" #userPassword="ngModel" [(ngModel)]="profile.password" required
              pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}">
            </ion-input>
            <i-feather name="eye" (click)="eyePassword()"
              *ngIf="profile.password?.length && passwordFieldType !== 'password'"></i-feather>
            <i-feather name="eye-off" (click)="eyePassword()"
              *ngIf="profile.password?.length && passwordFieldType === 'password'"></i-feather>
          </ion-item>
          <app-validation-message [field]="userPassword" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Password must contain a capital letter, number and special character & should be greater than 8 characters'">
          </app-validation-message>
        </div>

        <!-- Submit -->
        <ion-button class="margin-top-20 site-button" expand="full" shape="round" type="submit">
          <span>Continue</span>
        </ion-button>

      </form>
    </div>

    <!-- Terms -->
    <div class="privacy-container margin-top-110">
      <p>
        By continuing you agree to our
        <a href="https://www.google.com" target="_blank" rel="noopener noreferrer" class="terms-link">Terms
          of Service</a>
        and
        <a href="https://www.google.com" target="_blank" rel="noopener noreferrer" class="privacy-link">Privacy
          Policy</a>
      </p>
    </div>

  </div>
</ion-content>