<ion-content class="onboarding-page">

  <div class="onboarding-header-section">
    <img src="/assets/images/icons/header-image.png" />
    <img class="bees-logo-image" src="/assets/images/icons/bees-logo.png" />
  </div>

  <div class="onboarding-page-container">
    <!-- <div>
      <div class="profile-picture-wrapper">
        <div class="profile-picture-container">
          <img src="/assets/images/icons/user.png" *ngIf="!profile.profileImageUrl" alt="" />
          <img [src]="profile.profileImageUrl" class="profile-pic" *ngIf="profile.profileImageUrl"
            alt="Customer Profile" />
        </div>
      </div>
    </div> -->

    <div class="onboarding-container">
      <span class="page-heading">Let's get to know you</span>
      <span class="page-heading-title">What should we call you?</span>
    </div>

    <div class="onboarding-progress">
      <div class="onboarding-progress-completed" [ngStyle]="{'width': '50%'}"></div>
    </div>

    <!-- Login Form -->
    <div class="form-container">
      <form class="custom-form" #registerForm="ngForm" novalidate (ngSubmit)="continue(registerForm.form)">

        <div class="margin-top-20">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid': companyName.invalid && onClickValidation}">
            <ion-input name="companyName" #companyName="ngModel" [(ngModel)]="profile.customerDetail.companyName"
              required="required" maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="Company Name"
              labelPlacement="floating">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="companyName" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Only alphabetic characters are allowed.'">
          </app-validation-message>
        </div>

        <div class="margin-top-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':companyEmail.invalid && onClickValidation}">
            <ion-icon src="/assets/images/svg/email-icon.svg" slot="start" class="start-icon"></ion-icon>
            <ion-input #emailInput label="Email Id" labelPlacement="floating" required name="companyEmail"
              #companyEmail="ngModel" pattern="^[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,3}$"
              [(ngModel)]="profile.customerDetail.companyEmail">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="companyEmail" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid email address.'">
          </app-validation-message>
        </div>

        <!-- Submit -->
        <ion-button class="margin-top-20 site-button" expand="full" shape="round" type="submit">
          <span>Continue</span>
        </ion-button>

      </form>
    </div>

    <!-- Terms -->
    <div class="privacy-container margin-top-25">
      <p>
        By continuing you agree to our
        <a href="https://www.google.com" target="_blank" rel="noopener noreferrer" class="terms-link">Terms
          of Service</a>
        and
        <a href="https://www.google.com" target="_blank" rel="noopener noreferrer" class="privacy-link">Privacy
          Policy</a>
      </p>
    </div>

  </div>
</ion-content>