import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { ProfileDetail } from 'src/modals/profileDetail';
import { LocalStorageService } from 'src/shared/local-storage.service';

@Component({
  selector: 'app-driver-company-info',
  templateUrl: './driver-company-info.component.html',
  styleUrls: ['./driver-company-info.component.scss'],
  standalone: false
})
export class DriverCompanyInfoComponent implements OnInit {

  onClickValidation!: boolean;
  profile: ProfileDetail = new ProfileDetail();

  constructor(private readonly navController: NavController,
    private readonly localStorageService: LocalStorageService
  ) {

  }

  ngOnInit() {
    this.onClickValidation = false;
    const profile = this.localStorageService.getObject("CUSTOMER_ONBOARDING");
    this.profile = profile ? ProfileDetail.fromResponse(profile) : new ProfileDetail();
  }

  ionViewDidEnter() {
    const profile = this.localStorageService.getObject("CUSTOMER_ONBOARDING");
    this.profile = profile ? ProfileDetail.fromResponse(profile) : new ProfileDetail();
  }

  async continue(form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    this.localStorageService.setObject("CUSTOMER_ONBOARDING", this.profile);
    this.navController.navigateForward("/account/register/address", { animated: true });
  }

}
