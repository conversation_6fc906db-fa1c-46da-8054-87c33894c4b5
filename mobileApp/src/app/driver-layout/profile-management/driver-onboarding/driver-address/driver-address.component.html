<ion-content class="onboarding-page">

  <div class="onboarding-header-section">
    <img src="/assets/images/icons/header-image.png" />
    <img class="bees-logo-image" src="/assets/images/icons/bees-logo.png" />
  </div>

  <div class="onboarding-page-container">
    <!-- <div>
      <div class="profile-picture-wrapper">
        <div class="profile-picture-container">
          <img src="/assets/images/icons/user.png" *ngIf="!profile.profileImageUrl" alt="" />
          <img [src]="profile.profileImageUrl" class="profile-pic" *ngIf="profile.profileImageUrl"
            alt="Customer Profile" />
        </div>
      </div>
    </div> -->

    <div class="onboarding-container">
      <span class="page-heading">Let's get to know you</span>
      <span class="page-heading-title">What should we call you?</span>
    </div>

    <div class="onboarding-progress">
      <div class="onboarding-progress-completed" [ngStyle]="{'width': '100%'}"></div>
    </div>

    <!-- Login Form -->
    <div class="form-container">
      <form class="custom-form" #registerForm="ngForm" novalidate (ngSubmit)="continue(registerForm.form)">

        <div class="margin-top-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid': companyName.invalid && onClickValidation}">
            <ion-input name="companyName" #companyName="ngModel" [(ngModel)]="profile.customerDetail.companyName"
              required="required" maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="Company Name"
              labelPlacement="floating">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="companyName" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Only alphabetic characters are allowed.'">
          </app-validation-message>
        </div>

        <div class="margin-top-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':companyEmail.invalid && onClickValidation}">
            <ion-input #emailInput label="Company email" labelPlacement="floating" required name="companyEmail"
              #companyEmail="ngModel" pattern="^[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,3}$"
              [(ngModel)]="profile.customerDetail.companyEmail">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="companyEmail" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid email address.'">
          </app-validation-message>
        </div>

        <div class="margin-top-10" *ngIf="profile?.addressDetail">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid': address.invalid && onClickValidation}">
            <ion-input readonly name="address" #address="ngModel" [(ngModel)]="profile.addressDetail.address"
              required="required" mode="md" label="Address" labelPlacement="floating">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="address" [onClickValidation]="onClickValidation">
          </app-validation-message>
        </div>

        <div class="common-fields-container" *ngIf="profile?.addressDetail">
          <div class="field-container small-field">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': city.invalid && onClickValidation}">
              <ion-input readonly name="city" #city="ngModel" [(ngModel)]="profile.addressDetail.city"
                required="required" maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="City"
                labelPlacement="floating">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="city" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </div>

          <div class="field-container large-field">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': province.invalid && onClickValidation}">
              <ion-input readonly name="province" #province="ngModel" [(ngModel)]="profile.addressDetail.state"
                required="required" maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="Province"
                labelPlacement="floating">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="province" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </div>
        </div>

        <div class="common-fields-container" *ngIf="profile?.addressDetail">
          <div class="field-container small-field">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': postalCode.invalid && onClickValidation}">
              <ion-input readonly name="postalCode" #postalCode="ngModel" [(ngModel)]="profile.addressDetail.pin"
                required="required" mode="md" label="Postal Code" labelPlacement="floating">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="postalCode" [onClickValidation]="onClickValidation">
            </app-validation-message>
          </div>

          <div class="field-container large-field">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': country.invalid && onClickValidation}">
              <ion-input readonly name="country" #country="ngModel" [(ngModel)]="profile.addressDetail.country"
                required="required" maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="Country"
                labelPlacement="floating">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="country" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </div>
        </div>

        <!-- Submit -->
        <ion-button class="margin-top-20 site-button" expand="full" shape="round" type="submit">
          <span>Submit</span>
        </ion-button>

      </form>
    </div>

    <div class="register-container">
      <p>
        Already have an account? <a (click)="goToLogin()">Sign In</a>
      </p>
    </div>

    <!-- Terms -->
    <div class="privacy-container">
      <p>
        By continuing you agree to our
        <a href="https://www.google.com" target="_blank" rel="noopener noreferrer" class="terms-link">Terms
          of Service</a>
        and
        <a href="https://www.google.com" target="_blank" rel="noopener noreferrer" class="privacy-link">Privacy
          Policy</a>
      </p>
    </div>

  </div>
</ion-content>

<ion-modal class="site-custom-popup job-invitation-popup" #noAddressFoundPopup [isOpen]="isNoAddressFoundPopupOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeNoAddressFoundPopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <div class="popup-large-heading">Oops! Unable to fetch the address. Tap to retry.</div>
        <div class="popup-normal-heading margin-top-10 secondary-text">Note:- Please make sure that your location
          permission is
          granted.</div>
        <ion-button class="site-full-rounded-button primary-button margin-top-25" expand="full" shape="round"
          (click)="retry()">
          Retry
        </ion-button>
      </div>
    </div>
  </ng-template>
</ion-modal>