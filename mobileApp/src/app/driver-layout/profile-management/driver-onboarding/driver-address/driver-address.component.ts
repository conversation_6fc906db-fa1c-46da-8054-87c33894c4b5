import { Component, OnDestroy, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { AddressDetail, ProfileDetail } from 'src/modals/profileDetail';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { RestResponse } from 'src/shared/auth.model';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';
import { Geolocation } from '@capacitor/geolocation';
import { LoadingController } from '@ionic/angular';
import { maskitoGetCountryFromNumber } from '@maskito/phone';
import metadata from 'libphonenumber-js/metadata.min.json';
import { LoadingService } from 'src/services/loading.service';
import { StatusBarService } from 'src/services/status-bar.service';
import { Style } from '@capacitor/status-bar';


@Component({
  selector: 'app-driver-address',
  templateUrl: './driver-address.component.html',
  styleUrls: ['./driver-address.component.scss'],
  standalone: false
})
export class DriverAddressComponent implements OnInit, OnDestroy {

  onClickValidation!: boolean;
  isNoAddressFoundPopupOpen: boolean = false;
  profile: ProfileDetail = new ProfileDetail();
  subscription: Subscription = new Subscription();
  currentAddress: any;

  constructor(private readonly navController: NavController,
    public commonService: CommonService,
    private readonly localStorageService: LocalStorageService,
    private readonly dataService: DataService,
    private readonly toastService: ToastService,
    private readonly loadingController: LoadingController,
    private loadingService: LoadingService,
    private statusBarService: StatusBarService,
  ) {

  }

  ngOnInit() {
    this.onClickValidation = false;
    const profile = this.localStorageService.getObject("CUSTOMER_ONBOARDING");
    this.profile = profile ? ProfileDetail.fromResponse(profile) : new ProfileDetail();
  }
  ionViewWillEnter() {
    this.statusBarService.setColorScheme('authentication');

    // Or set custom colors
    this.statusBarService.setCustomStatusBar({
      backgroundColor: '#FDFBDF', // Orange
      style: Style.Light // Light text on dark background
    });
  }

  ionViewDidEnter() {
    const profile = this.localStorageService.getObject("CUSTOMER_ONBOARDING");
    this.profile = profile ? ProfileDetail.fromResponse(profile) : new ProfileDetail();

    if (!this.profile.addressDetail) {
      this.profile.addressDetail = new AddressDetail();
    }
    this.fetchAddressFromCurrentLocation();
  }

  openNoAddressFoundPopup() {
    this.isNoAddressFoundPopupOpen = true;
  }

  closeNoAddressFoundPopup() {
    this.isNoAddressFoundPopupOpen = false;
  }

  goToLogin() {
    this.localStorageService.remove("CUSTOMER_ONBOARDING");
    this.navController.navigateRoot("/account/login", { animated: true });
  }

  retry() {
    this.fetchAddressFromCurrentLocation();
    this.closeNoAddressFoundPopup();
  }

  async fetchAddressFromCurrentLocation() {
    const loading = await this.loadingController.create({
      message: 'Fetching address...',
      spinner: 'circles',
      cssClass: 'custom-loading-popup',
      backdropDismiss: true
    });

    await loading.present();
    try {
      const position = await Geolocation.getCurrentPosition({
        enableHighAccuracy: true,
        timeout: 15000
      });
      const lat = position.coords.latitude;
      const lng = position.coords.longitude;
      const response = await this.getAddressFromCoords(lat, lng);
      if (response?.results?.length > 0) {
        const addressComponents = response.results[0].address_components;
        const getComponent = (type: string) => {
          return addressComponents.find((c: { types: string[] }) => c.types.includes(type))?.long_name || '';
        };
        const fullAddress = response.results[0].formatted_address;
        this.profile.addressDetail.address = fullAddress;
        this.profile.addressDetail.city = getComponent('locality');
        this.profile.addressDetail.state = getComponent('administrative_area_level_1');
        this.profile.addressDetail.pin = getComponent('postal_code');
        this.profile.addressDetail.country = getComponent('country');

        this.profile.addressDetail.latitude = lat.toString();
        this.profile.addressDetail.longitude = lng.toString();
      } else {
        this.currentAddress = 'Address not found';
      }
      await loading.dismiss();

    } catch (error) {
      this.openNoAddressFoundPopup();
      await loading.dismiss();
    } finally {
      await loading.dismiss();
    }
  }

  async getAddressFromCoords(lat: number, lng: number): Promise<any> {
    const apiKey = 'AIzaSyDjwD-3pwe36dk5kv_dkEzsYGirWZPWCiY';
    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${apiKey}`;

    try {
      const response = await fetch(url);
      const data = await response.json();
      return data;
    } catch (e) {
      return null;
    }
  }

  async continue(form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    if (this.profile.phoneNumber) {
      const phone = this.profile.phoneNumber.trim();
      this.profile.phoneNumber = this.commonService.formatPhoneNumber(phone);
    }
    this.loadingService.show();
    this.subscription = this.dataService.customerOnboarding(this.profile)
      .subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          const data = response.data;
          this.localStorageService.remove("CUSTOMER_ONBOARDING");
          this.navController.navigateRoot("/account/login", { animated: true });
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message);
        }
      })
  }

  ngOnDestroy(): void {
    // Unsubscribe from any subscriptions to prevent memory leaks
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
