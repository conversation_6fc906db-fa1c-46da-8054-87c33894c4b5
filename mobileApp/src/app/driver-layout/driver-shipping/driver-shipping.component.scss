 .shipping-tab-page {
     --background: white;
     height: 100%;

     // Dropdown styling to match basic-info component
     .site-form-control {
         width: 100%;
         margin-bottom: 10px;

         ion-input,
         ion-select {
             font-size: 14px;
             --padding-start: 0px !important;
             --padding-end: 0px;
             --padding-top: 3px;
             --padding-bottom: 0px;
             font-weight: 500;
             min-height: 50px !important;
             width: 100%;
         }

         ion-select::part(icon) {
             display: none;
         }

         .dropdown-arrow-icon {
             margin-top: 19px;
             font-size: 16px;
         }

         .start-icon {
             margin-right: 13px;
             color: black;
             width: 18px;
             height: 18px;
         }

         .search-ion-input {
             font-size: 14px;
             --padding-start: 0px !important;
             --padding-end: 0px;
             --padding-top: 3px;
             --padding-bottom: 0px;
             font-weight: 500;
             min-height: 50px !important;
             width: 100%;
         }
     }

     .add-shipping-container {
         display: flex;
         justify-content: space-between;
         align-items: center;
         background: black;
         color: white;
         border-radius: 18px;
         padding: 17px 20px;
         gap: 5px;
         position: relative;
         overflow: hidden;
         cursor: pointer;
         transition: all 0.2s ease;
         user-select: none;

         &:hover {
             background: #333;
             transform: translateY(-1px);
             box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
         }

         &:active {
             transform: translateY(0);
             box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
         }

         .add-shipping-icon {
             font-size: 20px;
         }

         .add-shipping-text {
             font-size: 14px;
         }

         ion-ripple-effect {
             color: rgba(255, 255, 255, 0.3);
         }
     }

     // Fixed search section styling
     .fixed-search.shipment-page-search {
         padding: 0px 20px 10px 20px;
         background: white;

         .padding-top {
             padding-top: 10px;
         }
     }

     .shipping-tab-body-section {
         display: inline-block;
         width: 100%;
         height: calc(100vh - 295px); // Adjusted for additional dropdown
         padding: 10px 20px 10px 20px !important;
         overflow-y: auto;

         .shipping-tabs-items-container {
             display: flex;
             justify-content: center;
             align-items: center;
             padding-top: 2px;
             border-radius: 14px;
             background: #F9F9F9;
             box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);

             .shipping-tabs-items {
                 display: flex;
                 border-radius: 20px;
                 border: 3px solid #F9F9F9;

                 .shipping-tab-item {
                     width: 160px;
                     text-align: center;
                     padding: 12px 0;
                     font-size: 13px;
                     font-weight: 600;
                     color: #29385b;
                     cursor: pointer;
                     position: relative;
                     transition: all 0.3s ease;
                     border-radius: 14px;
                     overflow: hidden;
                     user-select: none;

                     &:hover {
                         background: rgba(255, 234, 0, 0.1);
                         transform: translateY(-1px);
                         box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                     }

                     &:active {
                         transform: translateY(0);
                     }

                     &.active {
                         border-top: 3px solid #FFEA00;
                         border-bottom: 3px solid #FFEA00;
                         background: #FFEA00;
                         color: #000;
                         font-weight: 700;
                     }

                     ion-ripple-effect {
                         color: rgba(255, 234, 0, 0.4);
                     }
                 }
             }
         }

         .shipping-action-list {
             display: flex;
             overflow-x: auto;
             padding: 10px 0;
             gap: 12px; // smaller gap for tighter scroll
             margin-top: 12px;

             &::-webkit-scrollbar {
                 display: none;
             }

             .shipping-action-item {
                 flex: 0 0 auto;
                 display: flex;
                 align-items: center;
                 justify-content: flex-start;
                 padding: 0px 15px 0px 0px;
                 background-color: #f8f8f8;
                 border-radius: 14px;
                 cursor: pointer;
                 gap: 10px;

                 .icon-container {
                     background-color: #ffea00;
                     border-radius: 50%;
                     padding: 10px;
                     display: flex;
                     align-items: center;
                     justify-content: center;
                 }

                 ion-icon {
                     font-size: 18px;
                     color: #000;
                 }

                 span {
                     font-size: 13px;
                     font-weight: 600;
                     color: #000;
                     white-space: nowrap;
                 }
             }
         }

         .shipping-card {
             background-color: #fff;
             border-radius: 18px;
             padding: 14px 16px;
             margin-bottom: 16px;
             box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
             position: relative;
             margin-top: 12px;
             overflow: hidden;

             &.active {
                 background-color: #ffea00;
             }

             .shipping-card-header {
                 display: flex;
                 justify-content: space-between;
                 align-items: start;
                 margin-bottom: 10px;
                 position: relative;
                 z-index: 1;

                 .ref-id {
                     font-size: 13px;
                     font-weight: 500;
                     color: #444;

                     strong {
                         font-weight: 700;
                         font-size: 14px;
                         color: #000;
                     }
                 }

                 .action-icons {
                     display: flex;
                     gap: 10px;

                     ion-icon {
                         font-size: 20px;
                         color: #000;
                         cursor: pointer;
                         position: relative;
                         overflow: hidden;
                         border-radius: 50%;
                         padding: 8px;
                         transition: all 0.2s ease;
                         user-select: none;

                         &:hover {
                             background: rgba(0, 0, 0, 0.1);
                             transform: scale(1.1);
                         }

                         &:active {
                             transform: scale(0.95);
                         }

                         &.edit-icon {
                             color: #000;

                             ion-ripple-effect {
                                 color: rgba(0, 0, 0, 0.2);
                             }
                         }

                         &.delete-icon {
                             color: red;

                             ion-ripple-effect {
                                 color: rgba(255, 0, 0, 0.2);
                             }
                         }
                     }
                 }
             }

             .customer-info {
                 margin-bottom: 12px;
                 padding: 8px 12px;
                 background: #f8f9fa;
                 border-radius: 8px;
                 position: relative;
                 z-index: 1;

                 .customer-name {
                     font-size: 12px;
                     color: #666;
                     line-height: 1.4;

                     strong {
                         color: #000;
                         font-size: 13px;
                         font-weight: 600;
                     }
                 }
             }

             .shipping-details {
                 display: flex;
                 align-items: center;
                 justify-content: space-between;

                 .payment-type {
                     flex: 1;
                     font-size: 13px;
                     position: relative;
                     z-index: 1;

                     strong {
                         font-weight: 700;
                     }
                 }

                 .location-timeline {
                     display: flex;
                     flex-direction: column;
                     align-items: center;
                     justify-content: center;
                     position: absolute;
                     width: 100%;
                     top: 17px;
                     z-index: 0;
                     left: 0;

                     .dot {
                         width: 16px;
                         height: 16px;
                         display: flex;
                         align-items: center;
                         justify-content: center;
                         border-radius: 50%;
                     }

                     .top-dot ion-icon {
                         width: 14px;
                         height: 14px;
                     }

                     .bottom-dot ion-icon {
                         width: 14px;
                         height: 14px;
                     }

                     .vertical-line {
                         width: 2px;
                         border: 2px dotted #000671;
                         min-height: 84px;
                     }
                 }

                 .locations {
                     flex: 1;
                     display: flex;
                     flex-direction: column;
                     gap: 8px;
                     text-align: right;
                     justify-content: flex-end;
                     align-items: flex-end;
                     z-index: 2;

                     .from,
                     .to {
                         font-size: 13px;

                         strong {
                             font-weight: 700;
                             color: #000;
                         }
                     }
                 }
             }

             .shipping-footer {
                 display: flex;
                 justify-content: space-between;
                 align-items: center;
                 position: relative;
                 z-index: 1;

                 .etd {
                     font-size: 13px;
                     font-weight: 500;

                     strong {
                         color: #000;
                     }
                 }

                 .status-tag {
                     font-size: 12px;
                     background-color: #FFEA00;
                     padding: 12px 22px;
                     border-radius: 8px;
                     font-weight: 600;
                     color: #000;
                     margin-top: 15px;
                 }

                 .status-container {
                     font-size: 11px;
                     background-color: #FFEA00;
                     padding: 8px 15px;
                     border-radius: 8px;
                     font-weight: 600;
                     color: #000;
                     margin-top: 15px;
                     display: flex;
                     gap: 5px;
                     align-items: center;
                     justify-content: center;

                     ion-icon {
                         font-size: 15px;
                         color: #000;
                     }
                 }

                 .total-amount {
                     font-size: 12px;
                     font-weight: 500;
                     text-align: right;

                     strong {
                         color: #000;
                         font-weight: 700;
                         font-size: 13px;
                     }
                 }

                 .action-button {
                     display: flex;
                     align-items: center;
                     gap: 8px;
                     background-color: #000;
                     color: white;
                     padding: 8px 16px;
                     border-radius: 20px;
                     font-size: 12px;
                     font-weight: 600;
                     cursor: pointer;
                     transition: all 0.3s ease;
                     margin-top: 10px;
                     position: relative;
                     overflow: hidden;
                     user-select: none;
                     box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);

                     &:hover {
                         background-color: #333;
                         transform: translateY(-2px);
                         box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                     }

                     &:active {
                         transform: translateY(-1px);
                         box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
                     }

                     .action-icon {
                         display: flex;
                         align-items: center;
                         justify-content: center;

                         ion-icon {
                             font-size: 16px;
                             color: white;
                         }
                     }

                     span {
                         white-space: nowrap;
                     }

                     ion-ripple-effect {
                         color: rgba(255, 255, 255, 0.3);
                     }
                 }

             }
         }

         .pagination-wrapper {
             display: flex;
             justify-content: center;
             align-items: center;
             gap: 8px;
             padding: 5px 0;

             .arrow-icon {
                 color: #000;
                 cursor: pointer;
                 border-radius: 50%;
                 width: 20px;
                 height: 20px;
                 display: flex;
                 justify-content: center;
                 align-items: center;
                 display: flex;
                 background: white;
                 padding: 6px;
                 border: 3px solid #f5f5f5;
                 box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);

                 &.arrow-image {
                     width: 38px;
                     height: 38px;
                 }
             }

             .page-number-container {
                 display: flex;
                 background: white;
                 padding: 4px;
                 border-radius: 23px;
                 border: 2px solid #f5f5f5;
                 box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
             }

             .page-number {
                 width: 30px;
                 height: 30px;
                 border-radius: 50%;
                 /* background-color: #f5f5f5; */
                 display: flex;
                 justify-content: center;
                 align-items: center;
                 font-weight: 500;
                 cursor: pointer;
                 color: #000;

                 &.active {
                     background-color: #ffea00;
                     color: #000;
                     border-radius: 10px;
                     font-weight: bold;
                 }
             }
         }

         .empty-state {
             display: flex;
             justify-content: center;
             align-items: center;
             height: 100%;
             min-height: 300px;

             .empty-content {
                 text-align: center;
                 color: #666;

                 .empty-icon {
                     font-size: 48px;
                     color: #ccc;
                     margin-bottom: 16px;
                 }

                 h3 {
                     margin: 0 0 8px 0;
                     font-size: 18px;
                     font-weight: 600;
                     color: #333;
                 }

                 p {
                     margin: 0;
                     font-size: 14px;
                     color: #666;
                 }
             }
         }
     }
 }