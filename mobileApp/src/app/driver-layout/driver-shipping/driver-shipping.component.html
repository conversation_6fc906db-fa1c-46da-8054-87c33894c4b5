<ion-content class="shipping-tab-page">
  <ion-refresher slot="fixed" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <app-customer-header [innerPage]="false" [headingText]="'Shipment Managment'"
    [rightAction]="true"></app-customer-header>

  <div class="fixed-search shipment-page-search">
    <div class="padding-top">
      <ion-item class="site-form-control" lines="none">
        <i-feather class="map-pin-icon start-icon" name="Search" slot="start"></i-feather>
        <ion-input class="search-ion-input" label="Search Here..." labelPlacement="floating" name="searchHere"
          [debounce]="500" (ionInput)="onSearchChange($event)"></ion-input>
      </ion-item>
    </div>
    <div class="add-shipping-container secondary-ripple" (click)="add()">
      <ion-icon class="add-shipping-icon" src="\assets\images\svg\add-icon.svg" slot="start"></ion-icon>
      <span class="add-shipping-text">Add</span>
      <ion-ripple-effect></ion-ripple-effect>
    </div>
  </div>

  <div class="shipping-tab-body-section">
    <div class="shipping-tabs-items-container">
      <div class="shipping-tabs-items">
        <div class="shipping-tab-item primary-ripple" [ngClass]="{'active': filter?.tabType === 'PENDING'}"
          (click)="onChangeStatusTab('PENDING')">
          Pending
          <ion-ripple-effect></ion-ripple-effect>
        </div>
        <div class="shipping-tab-item primary-ripple" [ngClass]="{'active': filter?.tabType === 'COMPLETED'}"
          (click)="onChangeStatusTab('COMPLETED')">
          Completed
          <ion-ripple-effect></ion-ripple-effect>
        </div>
      </div>
    </div>
    <div class="shipping-list-wrapper">
      <!-- Empty State -->
      <div class="empty-state" *ngIf="shippingList.length <= 0">
        <div class="empty-content">
          <ion-icon name="cube-outline" class="empty-icon"></ion-icon>
          <h3>No Shipments Found</h3>
          <p>{{ filter?.tabType === 'PENDING' ? 'No pending shipments available' : 'No completed shipments available' }}
          </p>
        </div>
      </div>

      <!-- Shipment Cards -->
      <div class="shipping-card" *ngFor="let item of shippingList">
        <div class="shipping-card-header">
          <div class="ref-id">
            Ref. Id<br /><strong>{{ item.refId }}</strong>
          </div>
          <div class="action-icons">
            <ion-icon class="edit-icon dark-ripple" src="/assets/images/svg/edit-icon.svg">
              <ion-ripple-effect></ion-ripple-effect>
            </ion-icon>
          </div>
        </div>

        <div class="customer-info" *ngIf="item.customerName && item.customerName !== 'N/A'">
          <div class="customer-name">
            Customer<br /><strong>{{ item.customerName }}</strong>
          </div>
        </div>

        <div class="shipping-details">
          <div class="payment-type">
            Payment Type<br />
            <strong>{{ item.paymentType }}</strong>
          </div>

          <div class="location-timeline">
            <div class="dot top-dot">
              <ion-icon src="/assets/images/svg/oval-location.svg"></ion-icon>
            </div>
            <div class="vertical-line"></div>
            <div class="dot bottom-dot">
              <ion-icon src="/assets/images/svg/red-location.svg"></ion-icon>
            </div>
          </div>

          <div class="locations">
            <div class="from">From<br /><strong>{{ item.from }}</strong></div>
            <div class="to">To<br /><strong>{{ item.to }}</strong></div>
          </div>
        </div>

        <div class="shipping-footer">
          <div class="etd">ETD<br /><strong>{{ item.etd }}</strong></div>
          <!-- <div class="status-tag">{{ item.status }}</div> -->
          <div class="total-amount" *ngIf="item.grandTotal > 0">
            Total<br /><strong>${{ item.grandTotal.toFixed(2) }}</strong>
          </div>
          <div class="action-button secondary-ripple" (click)="handleShipmentAction(item)">
            <div class="action-icon">
              <ion-icon [name]="getActionIcon(item)" slot="start"></ion-icon>
            </div>
            <span>{{ getActionText(item) }}</span>
            <ion-ripple-effect></ion-ripple-effect>
          </div>
        </div>
      </div>


    </div>
  </div>
</ion-content>