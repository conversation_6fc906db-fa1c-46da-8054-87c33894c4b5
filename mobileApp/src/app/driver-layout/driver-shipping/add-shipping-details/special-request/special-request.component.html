<ion-content class="special-request-page">
  <app-customer-header [innerPage]="true" [headingText]="'Shipment Management Special Request'" [rightAction]="false"
    [backUrl]="backUrl"></app-customer-header>

  <div class="special-request-body-section">
    <div class="form-container">
      <form class="custom-form" #requestForm="ngForm" novalidate>

        <!-- Oversize -->
        <div class="margin-top-20">
          <div class="request-container">
            <span class="request-text">Oversize</span>
            <div class="request-section-container margin-top-10">
              <div class="request-type-section-container ion-text-left">
                <div class="request-type-section-item self-padding" (click)="onViewChange('oversize', 'YES')"
                  [ngClass]="{'selected': specialRequestData.oversize}">
                  <ion-checkbox [checked]="specialRequestData.oversize" mode="ios" shape="round"></ion-checkbox>
                  <span>Yes</span>
                </div>
                <div class="request-type-section-item" (click)="onViewChange('oversize', 'NO')"
                  [ngClass]="{'selected': specialRequestData.oversize === false}">
                  <ion-checkbox [checked]="specialRequestData.oversize === false" mode="ios"
                    shape="round"></ion-checkbox>
                  <span>No</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Rush Request -->
          <div class="request-container margin-top-12">
            <span class="request-text">Rush Request Immediately</span>
            <div class="request-section-container margin-top-10">
              <div class="request-type-section-container ion-text-left">
                <div class="request-type-section-item self-padding" (click)="onViewChange('rushRequest', 'YES')"
                  [ngClass]="{'selected': specialRequestData.rushRequest}">
                  <ion-checkbox [checked]="specialRequestData.rushRequest" mode="ios" shape="round"></ion-checkbox>
                  <span>Yes</span>
                </div>
                <div class="request-type-section-item" (click)="onViewChange('rushRequest', 'NO')"
                  [ngClass]="{'selected': specialRequestData.rushRequest === false}">
                  <ion-checkbox [checked]="specialRequestData.rushRequest === false" mode="ios"
                    shape="round"></ion-checkbox>
                  <span>No</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Enclosed -->
          <div class="request-container margin-top-12">
            <span class="request-text">Enclosed</span>
            <div class="request-section-container margin-top-10">
              <div class="request-type-section-container ion-text-left">
                <div class="request-type-section-item self-padding" (click)="onViewChange('enclosed', 'YES')"
                  [ngClass]="{'selected': specialRequestData.enclosed}">
                  <ion-checkbox [checked]="specialRequestData.enclosed" mode="ios" shape="round"></ion-checkbox>
                  <span>Yes</span>
                </div>
                <div class="request-type-section-item" (click)="onViewChange('enclosed', 'NO')"
                  [ngClass]="{'selected': specialRequestData.enclosed === false}">
                  <ion-checkbox [checked]="specialRequestData.enclosed === false" mode="ios"
                    shape="round"></ion-checkbox>
                  <span>No</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Fragile -->
          <div class="request-container margin-top-12">
            <span class="request-text">Fragile</span>
            <div class="request-section-container margin-top-10">
              <div class="request-type-section-container ion-text-left">
                <div class="request-type-section-item self-padding" (click)="onViewChange('fragile', 'YES')"
                  [ngClass]="{'selected': specialRequestData.fragile}">
                  <ion-checkbox [checked]="specialRequestData.fragile" mode="ios" shape="round"></ion-checkbox>
                  <span>Yes</span>
                </div>
                <div class="request-type-section-item" (click)="onViewChange('fragile', 'NO')"
                  [ngClass]="{'selected': specialRequestData.fragile === false}">
                  <ion-checkbox [checked]="specialRequestData.fragile === false" mode="ios"
                    shape="round"></ion-checkbox>
                  <span>No</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Perishable -->
          <div class="request-container margin-top-12">
            <span class="request-text">Perishable</span>
            <div class="request-section-container margin-top-10">
              <div class="request-type-section-container ion-text-left">
                <div class="request-type-section-item self-padding" (click)="onViewChange('perishable', 'YES')"
                  [ngClass]="{'selected': specialRequestData.perishable}">
                  <ion-checkbox [checked]="specialRequestData.perishable" mode="ios" shape="round"></ion-checkbox>
                  <span>Yes</span>
                </div>
                <div class="request-type-section-item" (click)="onViewChange('perishable', 'NO')"
                  [ngClass]="{'selected': specialRequestData.perishable === false}">
                  <ion-checkbox [checked]="specialRequestData.perishable === false" mode="ios"
                    shape="round"></ion-checkbox>
                  <span>No</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Dangerous Goods -->
          <div class="request-container margin-top-12">
            <span class="request-text">Dangerous Goods</span>
            <div class="request-section-container margin-top-10">
              <div class="request-type-section-container ion-text-left">
                <div class="request-type-section-item self-padding" (click)="onViewChange('dangerousGoods', 'YES')"
                  [ngClass]="{'selected': specialRequestData.dangerousGoods}">
                  <ion-checkbox [checked]="specialRequestData.dangerousGoods" mode="ios" shape="round"></ion-checkbox>
                  <span>Yes</span>
                </div>
                <div class="request-type-section-item" (click)="onViewChange('dangerousGoods', 'NO')"
                  [ngClass]="{'selected': specialRequestData.dangerousGoods === false}">
                  <ion-checkbox [checked]="specialRequestData.dangerousGoods === false" mode="ios"
                    shape="round"></ion-checkbox>
                  <span>No</span>
                </div>
              </div>
            </div>
          </div>

        </div>

        <!-- Buttons -->
        <div class="shippment-btn-container">
          <ion-button class="margin-top-20 site-button ship-cancel-btn" expand="full" shape="round" type="submit"
            (click)="cancel()">
            <span>Cancel</span>
          </ion-button>
          <ion-button class="margin-top-20 site-button ship-submit-btn" expand="full" shape="round" type="submit"
            (click)="submitProfile(requestForm.form)">
            <span>Next</span>
          </ion-button>
        </div>

      </form>
    </div>
  </div>
</ion-content>