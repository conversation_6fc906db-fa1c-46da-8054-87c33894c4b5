import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { SpecialRequestStates } from 'src/modals/cargoDetail';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-special-request',
  templateUrl: './special-request.component.html',
  styleUrls: ['./special-request.component.scss'],
  standalone: false
})
export class SpecialRequestComponent implements OnInit {

  onClickValidation!: boolean;
  specialRequestData: SpecialRequestStates = new SpecialRequestStates();
  shipmentData: any;
  backUrl!: string;
  activeSubscriptions: Subscription = new Subscription();

  constructor(private readonly toastService: ToastService,
    private readonly navController: NavController,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    private readonly route: ActivatedRoute,
    public commonService: CommonService,
  ) {

  }

  ngOnInit(): void {
    const shippingData = this.localStorageService.getObject("SPECIAL_REQUEST");
    this.specialRequestData = shippingData ? SpecialRequestStates.fromResponse(shippingData) : new SpecialRequestStates();
  }

  ionViewWillEnter(): void {
    this.onClickValidation = false;
    const shippingData = this.localStorageService.getObject("SPECIAL_REQUEST");
    this.specialRequestData = shippingData ? SpecialRequestStates.fromResponse(shippingData) : new SpecialRequestStates();

    this.route.queryParams.subscribe(params => {
      this.shipmentData = params['shipmentData'] || null;
    });

    // Set the shipment ID in the special request data
    this.specialRequestData.id = this.shipmentData;

    this.backUrl = `/portal/cargo/listing?shipmentData=${this.shipmentData}`;
  }

  onViewChange(section: 'oversize' | 'rushRequest' | 'enclosed' | 'fragile' | 'perishable' | 'dangerousGoods', newValue: 'YES' | 'NO'): void {
    this.specialRequestData[section] = newValue === 'YES';
  }

  async submitProfile(form: any): Promise<void> {
    this.onClickValidation = true;

    if (!form.valid) {
      return;
    }

    this.localStorageService.setObject("SPECIAL_REQUEST", this.specialRequestData);
    this.navController.navigateForward('/portal/add/documents', {
      queryParams: {
        shipmentData: this.shipmentData
      }, animated: true
    });
  }

  cancel() {
    this.navController.navigateBack('/portal/cargo/listing', {
      queryParams: {
        shipmentData: this.shipmentData
      }, animated: true
    });
  }

}
