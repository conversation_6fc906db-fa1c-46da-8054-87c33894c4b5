 .special-request-page {
     --background: white;
     height: 100%;

     .special-request-body-section {
         display: inline-block;
         width: 100%;
         height: calc(100vh - 210px);
         padding: 0px 20px 10px 20px !important;
         overflow-y: auto;

         .form-container {
             min-height: 250px;
         }

         .info-text {
             font-size: 17px;
             font-weight: bold;
         }

         .request-container {
             .request-text {
                 font-size: 12px;
                 font-weight: 600;
                 color: black;
             }

             .request-section-container {
                 display: flex;
                 justify-content: space-between;
                 align-items: center;
             }
         }

         .request-type-section-container {
             display: flex;
             justify-content: center;
             width: 100%;
             gap: 10px;

             .request-type-section-item {
                 display: flex;
                 align-items: center;
                 //  max-width: 80px;
                 vertical-align: middle;
                 border: 1px solid #2c3c64 !important;
                 margin-right: 10px;
                 background: white;
                 border-radius: 10px;
                 padding: 5px 17px;

                 &.selected {
                     background: #FFEA00 !important;
                     border: unset !important;
                 }

                 &.disabled {
                     pointer-events: none;
                     opacity: 0.5;
                 }

                 ion-checkbox,
                 span {
                     display: inline-block;
                     vertical-align: middle;
                     padding: 4px;
                 }

                 span {
                     padding-left: 4px;
                     font-size: 13px;
                     font-weight: 500;
                     margin-top: 2px;
                 }
             }
         }

     }
 }