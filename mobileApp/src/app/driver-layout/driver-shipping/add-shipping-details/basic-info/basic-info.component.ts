import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { CommonService } from 'src/services/common.service';
import mask from './../../../../../shared/phone-number.mask';
import { maskitoGetCountryFromNumber } from '@maskito/phone';
import metadata from 'libphonenumber-js/min/metadata';
import { NgModel } from '@angular/forms';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { ToastService } from 'src/shared/toast.service';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ShippingBasicInfo } from 'src/modals/shipping-info';
import { ActivatedRoute } from '@angular/router';
import { RestResponse } from 'src/shared/auth.model';

@Component({
  selector: 'app-basic-info',
  templateUrl: './basic-info.component.html',
  styleUrls: ['./basic-info.component.scss'],
  standalone: false
})
export class BasicInfoComponent implements OnInit {

  onClickValidation!: boolean;
  refIdData!: string;
  selectedCustomerId!: string;
  shippingData: ShippingBasicInfo = new ShippingBasicInfo();
  activeSubscriptions: Subscription = new Subscription();
  isApple: boolean = false;
  code: string = "";
  protected readonly mask = mask;
  allCustomers: Array<any> = [];
  hasBasicInfo = true;

  constructor(
    private readonly navController: NavController,
    private readonly dataService: DataService,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    public commonService: CommonService,
    private readonly localStorageService: LocalStorageService,
    private readonly route: ActivatedRoute,
  ) { }

  ngOnInit(): void {
    const shippingData = this.localStorageService.getObject("SHIPPING_INFO");
    this.shippingData = shippingData ? ShippingBasicInfo.fromResponse(shippingData) : new ShippingBasicInfo();
  }

  ionViewWillEnter(): void {
    this.onClickValidation = false;

    this.route.queryParams.subscribe(params => {
      const shouldReset = params['reset'];

      if (shouldReset) {
        this.handleReset(params);
      } else {
        this.restoreFromStorage(params);
      }
    });

    this.shippingData.barcode = null;
    this.getAllCustomers();
  }

  private handleReset(params: any): void {
    this.shippingData = new ShippingBasicInfo();
    this.hasBasicInfo = false;
    this.selectedCustomerId = '';

    this.localStorageService.remove("SHIPPING_INFO");
    this.localStorageService.remove("SELECTED_CUSTOMER");

    this.refIdData = params['refIdData'] || null;
    if (this.refIdData) {
      this.shippingData.refID = this.refIdData;
    }

    // Force UI refresh
    setTimeout(() => {
      this.hasBasicInfo = true;
    }, 0);
  }

  private restoreFromStorage(params: any): void {
    const storedShipping = this.localStorageService.getObject("SHIPPING_INFO");
    this.shippingData = storedShipping ? ShippingBasicInfo.fromResponse(storedShipping) : new ShippingBasicInfo();

    const selectedCustomer = this.localStorageService.getObject("SELECTED_CUSTOMER");
    if (selectedCustomer?.id) {
      this.selectedCustomerId = selectedCustomer.id;
    }

    this.refIdData = params['refIdData'] || null;
    if (this.refIdData) {
      this.shippingData.refID = this.refIdData;
    }
  }

  protected get countryIsoCode(): string {
    const phone = this.shippingData?.contactPersonPhone;
    if (!phone || phone.trim() === '') return '';
    const code = maskitoGetCountryFromNumber(phone, metadata) ?? '';
    return code ? `/assets/images/icons/flags/ca.png` : '';
  }

  protected get pattern(): string {
    return '^\\+1\\d{10}$'; // +1 followed by exactly 10 digits
  }

  formatPhoneNumber(event: any, userPhone: NgModel): void {
    const input = event.target;
    let value: string = input.value || '';
    const digits = value.replace(/\D/g, '').slice(0, 10);
    let formatted = digits;
    if (digits.length > 6) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3, 6)} ${digits.slice(6)}`;
    } else if (digits.length > 3) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3)}`;
    }
    this.shippingData.contactPersonPhone = formatted;
    input.value = formatted;

    if (digits.length < 10) {
      userPhone.control.setErrors({ required: true });
    } else {
      userPhone.control.setErrors(null);
    }
  }

  getAllCustomers() {
    this.loadingService.show();
    this.dataService.getAllCustomers().subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();
        if (response?.data?.length) {
          this.allCustomers = response.data.map((customer: any) => ({
            id: customer.id,
            fullName: customer.fullName || '',
            customerDetail: customer.customerDetail || {},
            addressDetail: customer.addressDetail || {}  // Store full object
          }));
        }
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  onCustomerChange() {
    const selectedCustomer = this.allCustomers.find(c => c.id === this.selectedCustomerId);
    if (selectedCustomer) {
      this.shippingData.customer = selectedCustomer.id;

      const detail = selectedCustomer.customerDetail || {};

      this.shippingData.contactPersonName = detail.keyContact || '';
      this.shippingData.contactPersonEmail = detail.keyContactEmail || '';

      // Set and format phone if available
      if (detail.keyContactPhone) {
        const digits = detail.keyContactPhone.replace(/\D/g, '').slice(0, 10);
        let formatted = digits;
        if (digits.length > 6) {
          formatted = `${digits.slice(0, 3)}-${digits.slice(3, 6)} ${digits.slice(6)}`;
        } else if (digits.length > 3) {
          formatted = `${digits.slice(0, 3)}-${digits.slice(3)}`;
        }
        this.shippingData.contactPersonPhone = formatted;
      } else {
        this.shippingData.contactPersonPhone = '';
      }
      this.localStorageService.setObject("SELECTED_CUSTOMER", selectedCustomer);
    }
  }

  async submitProfile(form: any): Promise<void> {
    this.onClickValidation = true;
    if (!form.valid) return;

    this.localStorageService.setObject("SHIPPING_INFO", this.shippingData);
    this.navController.navigateForward("/portal/other/info", { animated: true });
  }

  cancel() {
    this.navController.navigateRoot("/portal/shipping", { animated: true });
  }
}
