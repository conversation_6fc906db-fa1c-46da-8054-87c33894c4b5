<ion-content class="basic-info-page">
  <app-customer-header [innerPage]="true" [headingText]="'Shipment Basic information'" [rightAction]="false"
    [backUrl]="'/portal/shipping'"></app-customer-header>

  <div class="basic-info-body-section">
    <div class="margin-top-25">
      <span class="info-text">Enter information</span>
    </div>
    <div class="form-container">
      <form class="custom-form" #basicForm="ngForm" novalidate>

        <div class="margin-top-20 margin-bottom-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{ 'is-invalid': refId.invalid && onClickValidation }">
            <ion-input readonly label="Ref. ID" labelPlacement="floating" name="refId" required
              [(ngModel)]="shippingData.refID" #refId="ngModel" mode="md">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="refId" [onClickValidation]="onClickValidation">
          </app-validation-message>
        </div>

        <div class="margin-top-10 margin-bottom-10">
          <ng-container *ngIf="hasBasicInfo">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': customers?.invalid && onClickValidation}">
              <ion-select #customers="ngModel" [(ngModel)]="selectedCustomerId" interface="action-sheet" required
                name="customer" (ionChange)="onCustomerChange()" label="All Customers" labelPlacement="floating">
                <ion-select-option *ngFor="let customer of allCustomers" [value]="customer.id">
                  {{ customer.fullName }}
                </ion-select-option>
              </ion-select>
              <ion-icon slot="end" class="dropdown-arrow-icon" [src]="'/assets/images/svg/down-arrow.svg'"></ion-icon>
            </ion-item>
            <app-validation-message [field]="customers" [onClickValidation]="onClickValidation">
            </app-validation-message>
          </ng-container>
        </div>

        <div class="margin-top-10 margin-bottom-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid': contactPerson.invalid && onClickValidation}">
            <ion-input name="contactPerson" #contactPerson="ngModel" [(ngModel)]="shippingData.contactPersonName"
              required="required" maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="Contact Person Name"
              labelPlacement="floating">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="contactPerson" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Only alphabetic characters are allowed.'">
          </app-validation-message>
        </div>

        <!-- Email -->
        <div class="margin-top-10 margin-bottom-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{ 'is-invalid': email.invalid && onClickValidation }">
            <ion-input label="Email" labelPlacement="floating" name="email" type="email" required
              pattern="^[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,3}$" [(ngModel)]="shippingData.contactPersonEmail"
              #email="ngModel">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="email" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid email address.'">
          </app-validation-message>
        </div>

        <!-- Phone Number -->
        <div class="margin-top-10 margin-bottom-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':userPhone.invalid && onClickValidation}">
            <ion-icon src="/assets/images/svg/canada-flag-icon.svg" slot="start" class="start-icon"></ion-icon>
            <ion-input inputmode="tel" required name="userPhone" #userPhone="ngModel"
              [(ngModel)]="shippingData.contactPersonPhone" placeholder="Phone Number"
              (ionInput)="formatPhoneNumber($event, userPhone)">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="userPhone" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid contact number.'">
          </app-validation-message>
        </div>

        <!-- Barcode -->
        <!-- <div class="margin-top-10 margin-bottom-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{ 'is-invalid': barcode.invalid && onClickValidation }">
            <ion-input readonly label="Barcode" labelPlacement="floating" name="barcode"
              [(ngModel)]="shippingData.barcode" #barcode="ngModel">
            </ion-input>
            <ion-icon slot="end" src="/assets/images/svg/barcode-outline.svg"></ion-icon>
          </ion-item>
          <app-validation-message [field]="barcode" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please scan qr.'">
          </app-validation-message>
        </div> -->

        <div class="shippment-btn-container">
          <ion-button class="margin-top-20 site-button ship-cancel-btn interactive-button" expand="full" shape="round"
            type="submit" (click)="cancel()">
            <span>Cancel</span>
            <ion-ripple-effect></ion-ripple-effect>
          </ion-button>
          <ion-button class="margin-top-20 site-button ship-submit-btn interactive-button" expand="full" shape="round"
            type="submit" (click)="submitProfile(basicForm.form)">
            <span>Next</span>
            <ion-ripple-effect></ion-ripple-effect>
          </ion-button>
        </div>

      </form>
    </div>

  </div>
</ion-content>