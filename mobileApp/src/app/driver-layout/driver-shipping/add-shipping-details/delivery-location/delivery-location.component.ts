import { Component, OnInit } from '@angular/core';
import { LoadingController, NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { Geolocation } from '@capacitor/geolocation';
import { ToastService } from 'src/shared/toast.service';
import mask from './../../../../../shared/phone-number.mask';
import { maskitoGetCountryFromNumber } from '@maskito/phone';
import metadata from 'libphonenumber-js/min/metadata';
import { NgModel } from '@angular/forms';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ShippingBasicInfo } from 'src/modals/shipping-info';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { CommonService } from 'src/services/common.service';

@Component({
  selector: 'app-delivery-location',
  templateUrl: './delivery-location.component.html',
  styleUrls: ['./delivery-location.component.scss'],
  standalone: false
})
export class DeliveryLocationComponent implements OnInit {

  onClickValidation!: boolean;
  isNoAddressFoundPopupOpen: boolean = false;
  shippingData: ShippingBasicInfo = new ShippingBasicInfo();
  activeSubscriptions: Subscription = new Subscription();
  isApple: boolean = false;
  code: string = "";
  protected readonly mask = mask;
  hasDeliveryData = true;

  constructor(private readonly toastService: ToastService,
    private readonly navController: NavController,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    private readonly loadingController: LoadingController,
    public commonService: CommonService,
  ) {

  }

  ngOnInit(): void {
    this.loadShippingData();
  }

  ionViewWillEnter(): void {
    this.onClickValidation = false;

    this.loadShippingData();
    this.evaluateDeliveryInfoVisibility();

    // Only auto-fetch if address is empty
    if (!this.shippingData.deliveryAddressDetail.address) {
      this.fetchAddressFromCurrentLocation();
    }
  }

  private loadShippingData(): void {
    const shippingData = this.localStorageService.getObject("SHIPPING_INFO");
    this.shippingData = shippingData ? ShippingBasicInfo.fromResponse(shippingData) : new ShippingBasicInfo();
  }

  private evaluateDeliveryInfoVisibility(): void {
    const info = this.shippingData;

    const missingRequiredFields = !info.deliveryCompanyName || !info.deliveryContactPersonName;
    if (missingRequiredFields) {
      this.hasDeliveryData = false;
      setTimeout(() => {
        this.hasDeliveryData = true;
      }, 0);
    } else {
      this.hasDeliveryData = true;
    }
  }

  openNoAddressFoundPopup() {
    this.isNoAddressFoundPopupOpen = true;
  }

  closeNoAddressFoundPopup() {
    this.isNoAddressFoundPopupOpen = false;
  }

  protected get countryIsoCode(): string {
    const phone = this.shippingData?.deliveryContactPersonPhone;
    if (!phone || phone.trim() === '') {
      return '';
    }

    const code = maskitoGetCountryFromNumber(phone, metadata) ?? '';
    return code ? `/assets/images/icons/flags/ca.png` : '';
  }

  protected get pattern(): string {
    return '^\\+1\\d{10}$'; // +1 followed by exactly 10 digits
  }

  formatPhoneNumber(event: any, userPhone: NgModel): void {
    const input = event.target;
    let value: string = input.value || '';

    // Remove all non-digit characters
    const digits = value.replace(/\D/g, '').slice(0, 10); // Max 10 digits

    let formatted = digits;
    if (digits.length > 6) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3, 6)} ${digits.slice(6)}`;
    } else if (digits.length > 3) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3)}`;
    }

    this.shippingData.deliveryContactPersonPhone = formatted;
    input.value = formatted;

    if (digits.length < 10) {
      userPhone.control.setErrors({ required: true });
    } else {
      userPhone.control.setErrors(null);
    }
  }

  retry() {
    this.fetchAddressFromCurrentLocation();
    this.closeNoAddressFoundPopup();
  }

  async checkLocationPermissions() {
    try {
      const permissions = await Geolocation.checkPermissions();
      console.log('Current location permissions:', permissions);
      console.log('Location permission state:', permissions.location);
      console.log('Coarse location permission state:', permissions.coarseLocation);

      let message = `Location: ${permissions.location}`;
      if (permissions.coarseLocation) {
        message += `, Coarse: ${permissions.coarseLocation}`;
      }

      // Add helpful guidance based on permission state
      if (permissions.location === 'denied') {
        message += '\n\nTo enable: Settings → Privacy → Location Services → [App Name] → While Using App';
      } else if (permissions.location === 'prompt') {
        message += '\n\nPermission not yet requested. Try the green key or red flash button.';
      } else if (permissions.location === 'granted') {
        message += '\n\nPermission granted! You can now fetch your address.';
      }

      this.toastService.show(message);
      return permissions;
    } catch (error) {
      console.error('Error checking permissions:', error);
      this.toastService.show('Error checking location permissions');
      return null;
    }
  }


  async requestLocationPermissions() {
    try {
      console.log('Manually requesting location permissions...');
      this.toastService.show('Requesting location permission...');

      // First check current status
      const currentPermissions = await Geolocation.checkPermissions();
      console.log('Current permissions before request:', currentPermissions);

      // Try to request permissions
      const result = await Geolocation.requestPermissions();
      console.log('Manual permission request result:', result);

      // Check if anything changed
      const newPermissions = await Geolocation.checkPermissions();
      console.log('Permissions after request:', newPermissions);

      let message = `Permission result - Location: ${result.location}`;
      if (result.coarseLocation) {
        message += `, Coarse: ${result.coarseLocation}`;
      }

      // If permission is still prompt, it means the dialog didn't show
      if (result.location === 'prompt' && currentPermissions.location === 'prompt') {
        message = 'Permission dialog did not appear. This may be an iOS/Capacitor issue.';
        console.warn('Permission dialog did not show - this is a known iOS/Capacitor issue');
      }

      this.toastService.show(message);
      return result;
    } catch (error: any) {
      console.error('Error requesting permissions:', error);
      this.toastService.show(`Error requesting location permissions: ${error.message || error}`);
      return null;
    }
  }

  async forceLocationRequest() {
    try {
      console.log('Attempting to force location request by calling getCurrentPosition...');
      this.toastService.show('Forcing location request via getCurrentPosition...');

      // Check current permission status
      const beforePermissions = await Geolocation.checkPermissions();
      console.log('Permissions before force request:', beforePermissions);

      // Method 1: Try getCurrentPosition with different options
      try {
        const position = await Geolocation.getCurrentPosition({
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 0
        });

        console.log('Force location request successful:', position);
        this.toastService.show('Location permission granted via getCurrentPosition!');

        // Check permissions after successful call
        const afterPermissions = await Geolocation.checkPermissions();
        console.log('Permissions after force request:', afterPermissions);

        return position;
      } catch (positionError: any) {
        console.log('getCurrentPosition failed, trying alternative methods...', positionError);

        // Method 2: Try with minimal options
        try {
          const position2 = await Geolocation.getCurrentPosition({
            enableHighAccuracy: false,
            timeout: 5000
          });

          console.log('Alternative force request successful:', position2);
          this.toastService.show('Location permission granted via alternative method!');
          return position2;
        } catch (altError: any) {
          console.log('Alternative method also failed:', altError);
          throw altError;
        }
      }

    } catch (error: any) {
      console.error('All force location methods failed:', error);

      // Check if permissions changed even though we got an error
      const finalPermissions = await Geolocation.checkPermissions();
      console.log('Final permissions after all attempts:', finalPermissions);

      // Check if it's a permission error
      const errorMessage = error.message || error.toString();
      console.log('Error message:', errorMessage);

      if (errorMessage?.includes('denied') || errorMessage?.includes('permission') || errorMessage?.includes('User denied')) {
        this.toastService.show('Location permission denied. Please enable in Settings → Privacy → Location Services');
      } else if (errorMessage?.includes('timeout') || errorMessage?.includes('TIMEOUT')) {
        this.toastService.show('Location request timed out. Permission may have been granted - check with info button.');
      } else if (errorMessage?.includes('unavailable') || errorMessage?.includes('POSITION_UNAVAILABLE')) {
        this.toastService.show('Location unavailable. Permission may have been granted - check with info button.');
      } else {
        this.toastService.show(`Force request failed: ${errorMessage}. Check permission status with info button.`);
      }

      return null;
    }
  }

  async ultimateForceRequest() {
    try {
      console.log('Attempting ultimate force request with multiple strategies...');
      this.toastService.show('Trying ultimate force request...');

      // Strategy 1: Request permissions first, then getCurrentPosition
      console.log('Strategy 1: Request permissions first...');
      const permissionResult = await Geolocation.requestPermissions();
      console.log('Permission request result:', permissionResult);

      if (permissionResult.location === 'granted') {
        console.log('Permission granted, now getting position...');
        const position = await Geolocation.getCurrentPosition({
          enableHighAccuracy: false,
          timeout: 5000
        });
        this.toastService.show('Ultimate force request successful!');
        return position;
      }

      // Strategy 2: Multiple getCurrentPosition calls with different settings
      console.log('Strategy 2: Multiple getCurrentPosition attempts...');
      const strategies = [
        { enableHighAccuracy: true, timeout: 15000, maximumAge: 0 },
        { enableHighAccuracy: false, timeout: 10000, maximumAge: 60000 },
        { enableHighAccuracy: true, timeout: 5000 },
        { enableHighAccuracy: false, timeout: 3000 }
      ];

      for (let i = 0; i < strategies.length; i++) {
        try {
          console.log(`Trying strategy ${i + 1}:`, strategies[i]);
          const position = await Geolocation.getCurrentPosition(strategies[i]);
          console.log(`Strategy ${i + 1} successful:`, position);
          this.toastService.show(`Ultimate force request successful with strategy ${i + 1}!`);
          return position;
        } catch (strategyError: any) {
          console.log(`Strategy ${i + 1} failed:`, strategyError.message);
          // Continue to next strategy
        }
      }

      throw new Error('All ultimate force strategies failed');

    } catch (error: any) {
      console.error('Ultimate force request failed:', error);

      // Final permission check
      const finalPermissions = await Geolocation.checkPermissions();
      console.log('Final permissions after ultimate attempt:', finalPermissions);

      if (finalPermissions.location === 'granted') {
        this.toastService.show('Permission is granted but location unavailable. Try the yellow location button.');
      } else {
        this.toastService.show('Ultimate force failed. You may need to manually enable location in iOS Settings.');
      }

      return null;
    }
  }

  async fetchAddressFromCurrentLocation() {
    let activeLoading = await this.loadingController.create({
      message: 'Fetching address...',
      spinner: 'circles',
      cssClass: 'custom-loading-popup',
      backdropDismiss: false
    });

    await activeLoading.present();

    const dismissLoading = async () => {
      try {
        await activeLoading?.dismiss();
      } catch (e) {
        // Loading already dismissed
      }
    };

    try {
      const permissions = await Geolocation.checkPermissions();
      console.log('Location permission state:', permissions.location);

      const locationPermissionGranted = permissions.location === 'granted';

      if (!locationPermissionGranted) {
        await dismissLoading();

        this.toastService.show('Please allow location access when prompted');
        await new Promise(resolve => setTimeout(resolve, 1000));

        const requestResult = await Geolocation.requestPermissions();
        console.log('Permission request result:', requestResult.location);

        if (requestResult.location !== 'granted') {
          this.toastService.show(
            requestResult.location === 'denied'
              ? 'Location permission denied. Please enable it in Settings > Privacy > Location Services.'
              : 'Location permission is required to fetch address'
          );
          return;
        }

        // Re-create loading
        activeLoading = await this.loadingController.create({
          message: 'Fetching address...',
          spinner: 'circles',
          cssClass: 'custom-loading-popup',
          backdropDismiss: false
        });
        await activeLoading.present();
      }

      // Fetch current location
      const position = await Geolocation.getCurrentPosition({
        enableHighAccuracy: true,
        timeout: 15000
      });

      const lat = position.coords.latitude;
      const lng = position.coords.longitude;
      console.log('Got position:', lat, lng);

      // Get address from lat/lng using reverse geocoding
      const response = await this.getAddressFromCoords(lat, lng);

      if (response?.results?.length > 0) {
        const addressComponents = response.results[0].address_components;
        const getComponent = (type: string) =>
          addressComponents.find((c: { types: string[] }) => c.types.includes(type))?.long_name || '';

        const fullAddress = response.results[0].formatted_address;

        this.shippingData.deliveryAddressDetail = {
          address: fullAddress,
          city: getComponent('locality'),
          state: getComponent('administrative_area_level_1'),
          pin: getComponent('postal_code'),
          country: getComponent('country'),
          latitude: lat.toString(),
          longitude: lng.toString()
        };

        this.toastService.show('Address fetched successfully!');
      } else {
        this.toastService.show('Address not found for current location');
      }

    } catch (error: any) {
      console.error('Error fetching address:', error);

      const message = error?.message?.toLowerCase() || '';

      if (message.includes('permission')) {
        this.toastService.show('Location permission is required to fetch address');
      } else if (message.includes('timeout')) {
        this.toastService.show('Location request timed out. Please try again.');
      } else if (message.includes('unavailable') || message.includes('position_unavailable')) {
        this.toastService.show('Location is currently unavailable. Please try again.');
      } else {
        this.openNoAddressFoundPopup();
      }
    } finally {
      await dismissLoading();
    }
  }
  async getAddressFromCoords(lat: number, lng: number): Promise<any> {
    const apiKey = 'AIzaSyDjwD-3pwe36dk5kv_dkEzsYGirWZPWCiY';
    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${apiKey}`;

    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();

      if (data.status === 'OK') {
        return data;
      } else {
        console.error('Geocoding API error:', data.status, data.error_message);
        throw new Error(`Geocoding failed: ${data.status}`);
      }
    } catch (e) {
      console.error('Error calling geocoding API:', e);
      throw e;
    }
  }

  async submitProfile(form: any): Promise<void> {
    this.onClickValidation = true;

    if (!form.valid) {
      return;
    }
    if (this.shippingData.contactPersonPhone) {
      const phone = this.shippingData.contactPersonPhone.trim();
      this.shippingData.contactPersonPhone = this.commonService.formatPhoneNumber(phone);
    }
    if (this.shippingData.pickupContactPersonPhone) {
      const phone = this.shippingData.pickupContactPersonPhone.trim();
      this.shippingData.pickupContactPersonPhone = this.commonService.formatPhoneNumber(phone);
    }
    if (this.shippingData.deliveryContactPersonPhone) {
      const phone = this.shippingData.deliveryContactPersonPhone.trim();
      this.shippingData.deliveryContactPersonPhone = this.commonService.formatPhoneNumber(phone);
    }
    this.loadingService.show();
    this.dataService.saveShipment(this.shippingData).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();
        this.toastService.show(response.message);

        const data = response.data;
        this.redirectToCargoList(data);
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  redirectToCargoList(data: any) {
    this.localStorageService.remove("SHIPPING_INFO");
    this.localStorageService.remove("SELECTED_CUSTOMER");
    this.navController.navigateForward('/portal/cargo/listing', {
      queryParams: {
        shipmentData: data
      }, animated: true
    });
  }

  cancel() {
    this.navController.navigateBack("/portal/pickup/delivery", { animated: true });
  }

}
