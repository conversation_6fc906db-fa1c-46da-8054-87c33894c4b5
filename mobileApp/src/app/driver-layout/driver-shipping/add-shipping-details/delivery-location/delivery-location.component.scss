 .delivery-location-page {
     --background: white;
     height: 100%;

     .delivery-location-body-section {
         display: inline-block;
         width: 100%;
         height: calc(100vh - 210px);
         padding: 0px 20px 10px 20px !important;
         overflow-y: auto;

         .form-container {
             min-height: 250px;
         }

         .info-text {
             font-size: 17px;
             font-weight: bold;
         }

         // Fetch address button styling
         .fetch-address-btn {
             --color: #FFEA00;
             --background: transparent;
             --border-radius: 50%;
             --padding-start: 8px;
             --padding-end: 8px;
             --padding-top: 8px;
             --padding-bottom: 8px;
             margin: 0;
             height: 40px;
             width: 40px;

             ion-icon {
                 font-size: 20px;
                 color: #FFEA00;
             }

             &:hover {
                 --background: rgba(255, 234, 0, 0.1);
             }
         }

         // Debug button styling
         .debug-btn {
             --color: #007bff;
             --background: transparent;
             --border-radius: 50%;
             --padding-start: 6px;
             --padding-end: 6px;
             --padding-top: 6px;
             --padding-bottom: 6px;
             margin: 0;
             height: 32px;
             width: 32px;

             ion-icon {
                 font-size: 16px;
                 color: #007bff;
             }

             &:hover {
                 --background: rgba(0, 123, 255, 0.1);
             }
         }

         // Request permission button styling
         .request-btn {
             --color: #28a745;
             --background: transparent;
             --border-radius: 50%;
             --padding-start: 6px;
             --padding-end: 6px;
             --padding-top: 6px;
             --padding-bottom: 6px;
             margin: 0;
             height: 32px;
             width: 32px;

             ion-icon {
                 font-size: 16px;
                 color: #28a745;
             }

             &:hover {
                 --background: rgba(40, 167, 69, 0.1);
             }
         }

         // Force location button styling
         .force-btn {
             --color: #dc3545;
             --background: transparent;
             --border-radius: 50%;
             --padding-start: 6px;
             --padding-end: 6px;
             --padding-top: 6px;
             --padding-bottom: 6px;
             margin: 0;
             height: 32px;
             width: 32px;

             ion-icon {
                 font-size: 16px;
                 color: #dc3545;
             }

             &:hover {
                 --background: rgba(220, 53, 69, 0.1);
             }
         }

         // Ultimate force button styling
         .ultimate-btn {
             --color: #6f42c1;
             --background: transparent;
             --border-radius: 50%;
             --padding-start: 6px;
             --padding-end: 6px;
             --padding-top: 6px;
             --padding-bottom: 6px;
             margin: 0;
             height: 32px;
             width: 32px;

             ion-icon {
                 font-size: 16px;
                 color: #6f42c1;
             }

             &:hover {
                 --background: rgba(111, 66, 193, 0.1);
             }
         }

         // Manual settings button styling
         .manual-btn {
             --color: #fd7e14;
             --background: transparent;
             --border-radius: 50%;
             --padding-start: 6px;
             --padding-end: 6px;
             --padding-top: 6px;
             --padding-bottom: 6px;
             margin: 0;
             height: 32px;
             width: 32px;

             ion-icon {
                 font-size: 16px;
                 color: #fd7e14;
             }

             &:hover {
                 --background: rgba(253, 126, 20, 0.1);
             }
         }
     }
 }