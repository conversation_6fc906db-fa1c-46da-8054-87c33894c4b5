<ion-content class="delivery-location-page">
  <app-customer-header [innerPage]="true" [headingText]="'Shipment Pickup & Delivery'" [rightAction]="false"
    [backUrl]="'/portal/pickup/delivery'"></app-customer-header>

  <div class="delivery-location-body-section">
    <div class="margin-top-20">
      <span class="info-text">Delivery Location</span>
    </div>
    <div class="form-container">
      <form class="custom-form" #deliveryForm="ngForm" novalidate>

        <div class="margin-top-20 margin-bottom-10">
          <ng-container *ngIf="hasDeliveryData">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{ 'is-invalid': companyName.invalid && onClickValidation }">
              <ion-input label="Delivery Company Name" labelPlacement="floating" name="companyName" required
                [(ngModel)]="shippingData.deliveryCompanyName" #companyName="ngModel" maxlength="100"
                pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="companyName" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </ng-container>
        </div>

        <div class="margin-top-10 margin-bottom-10">
          <ng-container *ngIf="hasDeliveryData">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{ 'is-invalid': contactName.invalid && onClickValidation }">
              <ion-input label="Delivery Contact Name" labelPlacement="floating" name="contactName" required
                [(ngModel)]="shippingData.deliveryContactPersonName" #contactName="ngModel" maxlength="100"
                pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="contactName" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </ng-container>
        </div>

        <div class="margin-top-10 margin-bottom-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':userPhone.invalid && onClickValidation}">
            <ion-icon src="/assets/images/svg/canada-flag-icon.svg" slot="start" class="start-icon"></ion-icon>
            <ion-input inputmode="tel" required name="userPhone" #userPhone="ngModel"
              [(ngModel)]="shippingData.deliveryContactPersonPhone" placeholder="Phone Number"
              (ionInput)="formatPhoneNumber($event, userPhone)">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="userPhone" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid contact number.'">
          </app-validation-message>
        </div>

        <div class="margin-top-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid': address.invalid && onClickValidation}">
            <ion-input name="address" #address="ngModel" [(ngModel)]="shippingData.deliveryAddressDetail.address"
              required="required" mode="md" label="Address" labelPlacement="floating">
            </ion-input>
            <ion-button slot="end" fill="clear" (click)="fetchAddressFromCurrentLocation()" class="fetch-address-btn">
              <ion-icon name="location-outline"></ion-icon>
            </ion-button>
            <!-- <ion-button slot="end" fill="clear" (click)="checkLocationPermissions()" class="debug-btn"
              style="margin-left: 5px;">
              <ion-icon name="information-circle-outline"></ion-icon>
            </ion-button>
            <ion-button slot="end" fill="clear" (click)="requestLocationPermissions()" class="request-btn"
              style="margin-left: 5px;">
              <ion-icon name="key-outline"></ion-icon>
            </ion-button>
            <ion-button slot="end" fill="clear" (click)="forceLocationRequest()" class="force-btn"
              style="margin-left: 5px;">
              <ion-icon name="flash-outline"></ion-icon>
            </ion-button>
            <ion-button slot="end" fill="clear" (click)="ultimateForceRequest()" class="ultimate-btn"
              style="margin-left: 5px;">
              <ion-icon name="nuclear-outline"></ion-icon>
            </ion-button>
            <ion-button slot="end" fill="clear" (click)="showManualSettingsInstructions()" class="manual-btn"
              style="margin-left: 5px;">
              <ion-icon name="settings-outline"></ion-icon>
            </ion-button> -->
          </ion-item>
          <app-validation-message [field]="address" [onClickValidation]="onClickValidation">
          </app-validation-message>
        </div>

        <div class="common-fields-container">
          <div class="field-container small-field">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': city.invalid && onClickValidation}">
              <ion-input name="city" #city="ngModel" [(ngModel)]="shippingData.deliveryAddressDetail.city"
                required="required" maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="City"
                labelPlacement="floating">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="city" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </div>

          <div class="field-container large-field">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': province.invalid && onClickValidation}">
              <ion-input name="province" #province="ngModel" [(ngModel)]="shippingData.deliveryAddressDetail.state"
                required="required" maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="Province"
                labelPlacement="floating">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="province" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </div>
        </div>

        <div class="common-fields-container">
          <div class="field-container small-field">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': postalCode.invalid && onClickValidation}">
              <ion-input name="postalCode" #postalCode="ngModel" [(ngModel)]="shippingData.deliveryAddressDetail.pin"
                required="required" mode="md" label="Postal Code" labelPlacement="floating">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="postalCode" [onClickValidation]="onClickValidation">
            </app-validation-message>
          </div>

          <div class="field-container large-field">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': country.invalid && onClickValidation}">
              <ion-input name="country" #country="ngModel" [(ngModel)]="shippingData.deliveryAddressDetail.country"
                required="required" maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="Country"
                labelPlacement="floating">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="country" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </div>
        </div>

        <div class="shippment-btn-container">
          <ion-button class="margin-top-20 site-button ship-cancel-btn" expand="full" shape="round" type="submit"
            (click)="cancel()">
            <span>Cancel</span>
          </ion-button>
          <ion-button class="margin-top-20 site-button ship-submit-btn" expand="full" shape="round" type="submit"
            (click)="submitProfile(deliveryForm.form)">
            <span>Submit</span>
          </ion-button>
        </div>

      </form>
    </div>

  </div>
</ion-content>

<ion-modal class="site-custom-popup job-invitation-popup" #noAddressFoundPopup [isOpen]="isNoAddressFoundPopupOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeNoAddressFoundPopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <div class="popup-large-heading">Oops! Unable to fetch the address. Tap to retry.</div>
        <div class="popup-normal-heading margin-top-10 secondary-text">Note:- Please make sure that your location
          permission is
          granted.</div>
        <ion-button class="site-full-rounded-button primary-button margin-top-25" expand="full" shape="round"
          (click)="retry()">
          Retry
        </ion-button>
      </div>
    </div>
  </ng-template>
</ion-modal>