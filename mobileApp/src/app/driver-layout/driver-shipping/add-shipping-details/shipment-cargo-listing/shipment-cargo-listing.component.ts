import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-shipment-cargo-listing',
  templateUrl: './shipment-cargo-listing.component.html',
  styleUrls: ['./shipment-cargo-listing.component.scss'],
  standalone: false
})
export class ShipmentCargoListingComponent implements OnInit {

  filter: any;
  currentPage = 4;
  shipmentData: any;
  shippingList: Array<any> = new Array<any>();

  searchQuery: string = '';  // Variable to hold the search query
  filteredCargoList: any[] = [];

  constructor(private readonly toastService: ToastService,
    private readonly navController: NavController,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    public commonService: CommonService,
    private readonly route: ActivatedRoute,
  ) { }

  ngOnInit() { }

  ionViewWillEnter() {
    this.filter = {} as any;
    this.filter.offset = 1;
    this.filter.tabType = 'PENDING'; // default is PENDING

    this.route.queryParams.subscribe(params => {
      this.shipmentData = params['shipmentData'] || null;
    });

    this.getCargoList();
  }

  getCargoList() {
    const payload = {
      filtering: {
        shipmentId: this.shipmentData
      }
    };
    this.loadingService.show();
    this.dataService.getCargoList(payload).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();

        const data = response.data;
        this.shippingList = data;
        this.filteredCargoList = [...this.shippingList];
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  onChangeStatusTab(status: string) {
    this.filter.tabType = status;
  }

  setPage(page: number) {
    this.currentPage = page;
  }

  goToPreviousPage() {
    if (this.currentPage > 1) {
      this.setPage(this.currentPage - 1);
    }
  }

  goToNextPage() {
    if (this.currentPage < 7) {
      this.setPage(this.currentPage + 1);
    }
  }

  searchCargoList() {
    this.filterCargoList();
  }

  filterCargoList() {
    if (this.searchQuery.trim()) {
      const query = this.searchQuery.toLowerCase();
      this.filteredCargoList = this.shippingList.filter(cargo =>
        cargo.cargoType?.toLowerCase().includes(query) ||
        cargo.description?.toLowerCase().includes(query) ||
        cargo.rateType?.toLowerCase().includes(query) ||
        cargo.weightType?.toLowerCase().includes(query)
      );
    } else {
      this.filteredCargoList = [...this.shippingList];
    }
  }

  add() {
    this.navController.navigateForward('/portal/add/cargo/details', {
      queryParams: {
        shipmentData: this.shipmentData
      }, animated: true
    });
  }

  specialRequest() {
    if (this.shippingList.length <= 0) {
      this.toastService.show("No cargo found. Please add a new cargo to continue.")
      return;
    }
    this.navController.navigateForward('/portal/special/request', {
      queryParams: {
        shipmentData: this.shipmentData
      }, animated: true
    });
  }

}
