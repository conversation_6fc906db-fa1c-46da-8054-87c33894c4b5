<ion-content class="add-documents-page">
  <app-customer-header [innerPage]="true" [headingText]="'Add Documents'" [rightAction]="false"
    [backUrl]="backUrl"></app-customer-header>

  <div class="add-documents-body-section">
    <div class="form-container">
      <form class="custom-form" #requestForm="ngForm" novalidate>

        <!-- Upload Box -->
        <div class="document-upload-container margin-top-20"
          [ngClass]="{'missing-image': showValidationErrors && !requestImage}">
          <img *ngIf="!requestImage" src="/assets/images/svg/attach.svg" alt="Upload Icon" class="upload-icon">
          <div class="upload-text" *ngIf="!requestImage">
            <span class="choose-file-text">Choose a file or drag & drop it here</span>
            <span class="upload-multiple-file-text">upload multiple or single file upload (max. 2mb)</span>
            <ion-button class="browse-file-button margin-top-20" expand="full" shape="round" type="submit"
              (click)="upload()">
              Browse file
            </ion-button>
          </div>
        </div>

        <!-- Image Gallery Below -->
        <div class="image-grid margin-top-30">
          <div class="image-item" *ngFor="let image of uploadedImages" (click)="toggleSelected(image)"
            [class.selected]="image.selected">
            <img [src]="image.url" alt="Uploaded" />
            <div class="icon-container" *ngIf="image.selected">
              <ion-icon class="icon" src="/assets/images/svg/view-icon.svg"
                (click)="viewImage(image.url,image.fileName)"></ion-icon>
              <ion-icon class="icon" src="/assets/images/svg/delete-icon.svg"
                (click)="onDeleteClick($event, image)"></ion-icon>
            </div>
          </div>
        </div>

        <div class="shippment-btn-container">
          <ion-button class="margin-top-20 site-button ship-cancel-btn interactive-button" expand="full" shape="round"
            type="submit" (click)="cancel()">
            <span class="cancel-text">Cancel</span>
            <ion-ripple-effect></ion-ripple-effect>
          </ion-button>
          <ion-button class="margin-top-20 site-button ship-submit-btn interactive-button" expand="full" shape="round"
            type="submit" (click)="submitProfile(requestForm.form)">
            <span>Submit</span>
            <ion-ripple-effect></ion-ripple-effect>
          </ion-button>
        </div>

      </form>
    </div>

  </div>
</ion-content>