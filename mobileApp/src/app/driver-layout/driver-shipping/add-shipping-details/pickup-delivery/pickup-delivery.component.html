<ion-content class="pickup-delivery-page">
  <app-customer-header [innerPage]="true" [headingText]="'Shipment Pickup & Delivery'" [rightAction]="false"
    [backUrl]="'/portal/other/info'"></app-customer-header>

  <div class="pickup-delivery-body-section">
    <div class="margin-top-20">
      <span class="info-text">Pickup Location</span>
    </div>
    <div class="form-container">
      <form class="custom-form" #pickupForm="ngForm" novalidate>

        <div class="margin-top-20 margin-bottom-10">
          <ng-container *ngIf="hasPickupDeliveryData">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{ 'is-invalid': companyName.invalid && onClickValidation }">
              <ion-input label="Pickup Company Name" labelPlacement="floating" name="companyName" required
                [(ngModel)]="shippingData.pickupCompanyName" #companyName="ngModel" maxlength="100"
                pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="companyName" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </ng-container>
        </div>

        <div class="margin-top-10 margin-bottom-10">
          <ng-container *ngIf="hasPickupDeliveryData">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{ 'is-invalid': contactName.invalid && onClickValidation }">
              <ion-input label="Pickup Contact Name" labelPlacement="floating" name="contactName" required
                [(ngModel)]="shippingData.pickupContactPersonName" #contactName="ngModel" maxlength="100"
                pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="contactName" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </ng-container>
        </div>

        <div class="margin-top-10 margin-bottom-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':userPhone.invalid && onClickValidation}">
            <ion-icon src="/assets/images/svg/canada-flag-icon.svg" slot="start" class="start-icon"></ion-icon>
            <ion-input inputmode="tel" required name="userPhone" #userPhone="ngModel"
              [(ngModel)]="shippingData.pickupContactPersonPhone" placeholder="Phone Number"
              (ionInput)="formatPhoneNumber($event, userPhone)">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="userPhone" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid contact number.'">
          </app-validation-message>
        </div>

        <div class="margin-top-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid': address.invalid && onClickValidation}">
            <ion-input name="address" #address="ngModel" [(ngModel)]="shippingData.pickupAddressDetail.address"
              required="required" mode="md" label="Address" labelPlacement="floating">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="address" [onClickValidation]="onClickValidation">
          </app-validation-message>
        </div>

        <div class="common-fields-container">
          <div class="field-container small-field">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': city.invalid && onClickValidation}">
              <ion-input name="city" #city="ngModel" [(ngModel)]="shippingData.pickupAddressDetail.city"
                required="required" maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="City"
                labelPlacement="floating">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="city" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </div>

          <div class="field-container large-field">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': province.invalid && onClickValidation}">
              <ion-input name="province" #province="ngModel" [(ngModel)]="shippingData.pickupAddressDetail.state"
                required="required" maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="Province"
                labelPlacement="floating">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="province" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </div>
        </div>

        <div class="common-fields-container">
          <div class="field-container small-field">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': postalCode.invalid && onClickValidation}">
              <ion-input name="postalCode" #postalCode="ngModel" [(ngModel)]="shippingData.pickupAddressDetail.pin"
                required="required" mode="md" label="Postal Code" labelPlacement="floating">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="postalCode" [onClickValidation]="onClickValidation">
            </app-validation-message>
          </div>

          <div class="field-container large-field">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': country.invalid && onClickValidation}">
              <ion-input name="country" #country="ngModel" [(ngModel)]="shippingData.pickupAddressDetail.country"
                required="required" maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="Country"
                labelPlacement="floating">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="country" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </div>
        </div>

        <div class="shippment-btn-container">
          <ion-button class="margin-top-20 site-button ship-cancel-btn interactive-button" expand="full" shape="round"
            type="submit" (click)="cancel()">
            <span>Cancel</span>
            <ion-ripple-effect></ion-ripple-effect>
          </ion-button>
          <ion-button class="margin-top-20 site-button ship-submit-btn interactive-button" expand="full" shape="round"
            type="submit" (click)="submitProfile(pickupForm.form)">
            <span>Next</span>
            <ion-ripple-effect></ion-ripple-effect>
          </ion-button>
        </div>

      </form>
    </div>

  </div>
</ion-content>