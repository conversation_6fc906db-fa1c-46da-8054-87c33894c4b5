import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { CommonService } from 'src/services/common.service';
import { ToastService } from 'src/shared/toast.service';
import mask from './../../../../../shared/phone-number.mask';
import { maskitoGetCountryFromNumber } from '@maskito/phone';
import metadata from 'libphonenumber-js/min/metadata';
import { isValidPhoneNumber } from 'libphonenumber-js/core';
import { NgModel } from '@angular/forms';
import { ShippingBasicInfo } from 'src/modals/shipping-info';
import { LocalStorageService } from 'src/shared/local-storage.service';

@Component({
  selector: 'app-pickup-delivery',
  templateUrl: './pickup-delivery.component.html',
  styleUrls: ['./pickup-delivery.component.scss'],
  standalone: false
})
export class PickupDeliveryComponent implements OnInit {

  onClickValidation!: boolean;
  selectedCustomer: any;
  shippingData: ShippingBasicInfo = new ShippingBasicInfo();
  activeSubscriptions: Subscription = new Subscription();
  isApple: boolean = false;
  code: string = "";
  protected readonly mask = mask;
  hasPickupDeliveryData = true;

  constructor(
    private readonly navController: NavController,
    public commonService: CommonService,
    private toastService: ToastService,
    private readonly localStorageService: LocalStorageService

  ) {

  }

  ngOnInit(): void {
    this.loadShippingData();
  }

  ionViewWillEnter(): void {
    this.onClickValidation = false;

    this.loadShippingData();
    this.selectedCustomers();
    this.evaluatePickupInfoVisibility();
  }

  private loadShippingData(): void {
    const shippingData = this.localStorageService.getObject("SHIPPING_INFO");
    this.shippingData = shippingData ? ShippingBasicInfo.fromResponse(shippingData) : new ShippingBasicInfo();
  }

  private selectedCustomers(): void {
    this.selectedCustomer = this.localStorageService.getObject("SELECTED_CUSTOMER");

    // Prefill pickup company name and contact name from selected customer
    if (this.selectedCustomer) {
      const customerDetail = this.selectedCustomer.customerDetail || {};

      // Set company name from customer's company name or full name as fallback
      if (!this.shippingData.pickupCompanyName) {
        this.shippingData.pickupCompanyName = customerDetail.companyName || this.selectedCustomer.fullName || '';
      }

      // Set contact name from customer's key contact or full name as fallback
      if (!this.shippingData.pickupContactPersonName) {
        this.shippingData.pickupContactPersonName = customerDetail.keyContact || this.selectedCustomer.fullName || '';
      }

      // Set contact phone from customer's key contact phone and format it
      if (!this.shippingData.pickupContactPersonPhone && customerDetail.keyContactPhone) {
        const digits = customerDetail.keyContactPhone.replace(/\D/g, '').slice(0, 10);
        let formatted = digits;
        if (digits.length > 6) {
          formatted = `${digits.slice(0, 3)}-${digits.slice(3, 6)} ${digits.slice(6)}`;
        } else if (digits.length > 3) {
          formatted = `${digits.slice(0, 3)}-${digits.slice(3)}`;
        }
        this.shippingData.pickupContactPersonPhone = formatted;
      }
    }

    // Prefill address details from selected customer
    this.shippingData.pickupAddressDetail.address = this.selectedCustomer?.addressDetail?.address;
    this.shippingData.pickupAddressDetail.city = this.selectedCustomer?.addressDetail?.city;
    this.shippingData.pickupAddressDetail.state = this.selectedCustomer?.addressDetail?.state;
    this.shippingData.pickupAddressDetail.pin = this.selectedCustomer?.addressDetail?.pin;
    this.shippingData.pickupAddressDetail.country = this.selectedCustomer?.addressDetail?.country;
    this.shippingData.pickupAddressDetail.latitude = this.selectedCustomer?.addressDetail?.latitude;
    this.shippingData.pickupAddressDetail.longitude = this.selectedCustomer?.addressDetail?.longitude;
  }

  private evaluatePickupInfoVisibility(): void {
    const info = this.shippingData;

    const missingRequiredFields = !info.pickupCompanyName || !info.pickupContactPersonName;
    if (missingRequiredFields) {
      this.hasPickupDeliveryData = false;
      setTimeout(() => {
        this.hasPickupDeliveryData = true;
      }, 0);
    } else {
      this.hasPickupDeliveryData = true;
    }
  }

  protected get countryIsoCode(): string {
    const phone = this.shippingData?.pickupContactPersonPhone;
    if (!phone || phone.trim() === '') {
      return '';
    }

    const code = maskitoGetCountryFromNumber(phone, metadata) ?? '';
    //  return code ? `/assets/images/icons/flags/${code.toLowerCase()}.png` : '';
    return code ? `/assets/images/icons/flags/ca.png` : '';
  }

  protected get pattern(): string {
    return '^\\+1\\d{10}$'; // +1 followed by exactly 10 digits
  }

  formatPhoneNumber(event: any, userPhone: NgModel): void {
    const input = event.target;
    let value: string = input.value || '';

    // Remove all non-digit characters
    const digits = value.replace(/\D/g, '').slice(0, 10); // Max 10 digits

    let formatted = digits;
    if (digits.length > 6) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3, 6)} ${digits.slice(6)}`;
    } else if (digits.length > 3) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3)}`;
    }

    this.shippingData.pickupContactPersonPhone = formatted;
    input.value = formatted;

    if (digits.length < 10) {
      userPhone.control.setErrors({ required: true });
    } else {
      userPhone.control.setErrors(null);
    }
  }

  async submitProfile(form: any): Promise<void> {
    this.onClickValidation = true;

    if (!form.valid) {
      return;
    }
    this.localStorageService.setObject("SHIPPING_INFO", this.shippingData);
    this.navController.navigateForward("/portal/delivery/location", { animated: true });
  }

  cancel() {
    this.navController.navigateBack("/portal/other/info", { animated: true });
  }

}
