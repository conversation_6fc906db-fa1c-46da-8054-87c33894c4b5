<ion-content class="add-cargo-detail-page">
  <app-customer-header [innerPage]="true" [headingText]="'Shipment Management'" [rightAction]="false"
    [backUrl]="backUrl"></app-customer-header>

  <div class="add-cargo-detail-body-section">
    <div class="margin-top-20">
      <span class="info-text">Enter Cargo Details</span>
    </div>
    <div class="form-container">
      <form class="custom-form" #addCargoDetailsForm="ngForm" novalidate>

        <div class="margin-top-20 margin-bottom-10">
          <ng-container *ngIf="hasCargoDetails">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': desc.invalid && onClickValidation}">
              <ion-textarea label="Description" labelPlacement="floating" name="desc" #desc="ngModel"
                [(ngModel)]="addCargoDetails.description" required="required" pattern="^(?!\s*$)[A-Za-z\s]+$">
              </ion-textarea>
            </ion-item>
            <app-validation-message [field]="desc" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </ng-container>
        </div>

        <div class="margin-top-10">
          <ng-container *ngIf="hasCargoDetails">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': types.invalid && onClickValidation}">
              <ion-select #types="ngModel" [(ngModel)]="addCargoDetails.cargoType" interface="action-sheet"
                required="required" name="types" label="Cargo Type" labelPlacement="floating">
                <ion-select-option *ngFor="let type of cargoTypes" [value]="type.value">
                  {{ type.display }}
                </ion-select-option>
              </ion-select>
              <ion-icon slot="end" class="down-arrow-icon" [src]="'/assets/images/svg/down-arrow.svg'"></ion-icon>
            </ion-item>
            <app-validation-message [field]="types" [onClickValidation]="onClickValidation">
            </app-validation-message>
          </ng-container>
        </div>

        <div class="margin-top-10">
          <ng-container *ngIf="hasCargoDetails">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': weighttypes.invalid && onClickValidation}">
              <ion-select #weighttypes="ngModel" [(ngModel)]="addCargoDetails.weightType" interface="action-sheet"
                required="required" name="weighttypes" (ionChange)="onWeightChange()" label="Weight Type"
                labelPlacement="floating">
                <ion-select-option *ngFor="let type of weightTypes" [value]="type.value">
                  {{ type.display }}
                </ion-select-option>
              </ion-select>
              <ion-icon slot="end" class="down-arrow-icon" [src]="'/assets/images/svg/down-arrow.svg'"></ion-icon>
            </ion-item>
            <app-validation-message [field]="weighttypes" [onClickValidation]="onClickValidation">
            </app-validation-message>
          </ng-container>
        </div>

        <div class="margin-top-10">
          <ng-container *ngIf="hasCargoDetails">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': ratetypes.invalid && onClickValidation}">
              <ion-select #ratetypes="ngModel" [(ngModel)]="addCargoDetails.rateType" interface="action-sheet"
                required="required" name="ratetypes" label="Rate Type" labelPlacement="floating">
                <ion-select-option *ngFor="let type of rateTypes" [value]="type.value">
                  {{ type.display }}
                </ion-select-option>
              </ion-select>
              <ion-icon slot="end" class="down-arrow-icon" [src]="'/assets/images/svg/down-arrow.svg'"></ion-icon>
            </ion-item>
            <app-validation-message [field]="ratetypes" [onClickValidation]="onClickValidation">
            </app-validation-message>
          </ng-container>
        </div>

        <div class="common-fields-container">
          <div class="field-container small-field">
            <ng-container *ngIf="hasCargoDetails">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': weight.invalid && onClickValidation}">
                <ion-input name="weight" #weight="ngModel" [(ngModel)]="addCargoDetails.weight" required="required"
                  mode="md" type="number"
                  [label]="addCargoDetails.weightType ? ('Weight (' + (addCargoDetails.weightType.toUpperCase() === 'LBS' ? 'pounds' : 'kg') + ')') : 'Weight (Kg/Pounds)'"
                  labelPlacement="floating" (ionInput)="onWeightChange()">
                </ion-input>
              </ion-item>
              <app-validation-message [field]="weight" [onClickValidation]="onClickValidation">
              </app-validation-message>
            </ng-container>
          </div>

          <div class="field-container large-field">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': calculate.invalid && onClickValidation}">
              <ion-input name="calculate" #calculate="ngModel" [(ngModel)]="addCargoDetails.weightInPounds"
                required="required" mode="md" label="Auto Calculated" labelPlacement="floating" readonly>
              </ion-input>
            </ion-item>
            <app-validation-message [field]="calculate" [onClickValidation]="onClickValidation">
            </app-validation-message>
          </div>
        </div>

        <div class="dimension-fields-container margin-top-10">
          <div class="dimension-row">
            <ng-container *ngIf="hasCargoDetails">
              <ion-item class="dim-item" lines="none" [ngClass]="{'is-invalid': length.invalid && onClickValidation}">
                <ion-input #length="ngModel" name="length" [(ngModel)]="addCargoDetails.length" required label="L in"
                  labelPlacement="floating" mode="md" type="number" (ngModelChange)="calculateVolume()">
                </ion-input>
              </ion-item>
            </ng-container>
            <span class="dim-symbol">×</span>
            <ng-container *ngIf="hasCargoDetails">
              <ion-item class="dim-item" lines="none" [ngClass]="{'is-invalid': width.invalid && onClickValidation}">
                <ion-input #width="ngModel" name="width" [(ngModel)]="addCargoDetails.width" required label="W in"
                  labelPlacement="floating" mode="md" type="number" (ngModelChange)="calculateVolume()">
                </ion-input>
              </ion-item>
            </ng-container>
            <span class="dim-symbol">×</span>
            <ng-container *ngIf="hasCargoDetails">
              <ion-item class="dim-item" lines="none" [ngClass]="{'is-invalid': height.invalid && onClickValidation}">
                <ion-input #height="ngModel" name="height" [(ngModel)]="addCargoDetails.height" required label="H in"
                  labelPlacement="floating" mode="md" type="number" (ngModelChange)="calculateVolume()">
                </ion-input>
              </ion-item>
            </ng-container>
            <span class="dim-symbol">×</span>
            <ng-container *ngIf="hasCargoDetails">
              <ion-item class="dim-item volume" lines="none"
                [ngClass]="{'is-invalid': volume.invalid && onClickValidation}">
                <ion-input required #volume="ngModel" name="volume" [(ngModel)]="addCargoDetails.volume" label="Volume"
                  labelPlacement="floating" mode="md" readonly>
                </ion-input>
              </ion-item>
            </ng-container>
          </div>
        </div>

        <div class="margin-top-10 quantity-field-container">
          <ion-item class="site-form-control quantity-input-wrapper" lines="none"
            [ngClass]="{'is-invalid': quantity.invalid && onClickValidation}">

            <div class="quantity-left-group" slot="start">
              <span class="quantity-label">Quantity</span>
              <div class="vertical-separator"></div>
              <span class="quantity-display">{{ addCargoDetails.quantity }}</span>
              <!-- Hidden input for validation -->
              <ion-input type="number" name="quantity" #quantity="ngModel" [(ngModel)]="addCargoDetails.quantity"
                required min="1" style="display: none">
              </ion-input>
            </div>

            <div class="quantity-controls" slot="end">
              <ion-icon class="control-icon up" src="/assets/images/svg/up-icon.svg"
                (click)="incrementQuantity()"></ion-icon>
              <ion-icon class="control-icon down" src="/assets/images/svg/down-icon.svg"
                (click)="decrementQuantity()"></ion-icon>
            </div>
          </ion-item>
          <app-validation-message [field]="quantity" [onClickValidation]="onClickValidation"></app-validation-message>
        </div>

        <div class="shippment-btn-container">
          <ion-button class="margin-top-20 site-button ship-cancel-btn interactive-button" expand="full" shape="round"
            type="submit" (click)="cancel()">
            <span class="cancel-text">Cancel</span>
            <ion-ripple-effect></ion-ripple-effect>
          </ion-button>
          <ion-button class="margin-top-20 site-button ship-submit-btn interactive-button" expand="full" shape="round"
            type="submit" (click)="submitProfile(addCargoDetailsForm.form)">
            <span>Submit</span>
            <ion-ripple-effect></ion-ripple-effect>
          </ion-button>
        </div>

      </form>
    </div>

  </div>
</ion-content>