import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { CargoDetails } from 'src/modals/cargoDetail';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-add-cargo-details',
  templateUrl: './add-cargo-details.component.html',
  styleUrls: ['./add-cargo-details.component.scss'],
  standalone: false
})
export class AddCargoDetailsComponent implements OnInit {

  onClickValidation!: boolean;
  addCargoDetails: CargoDetails = new CargoDetails();
  activeSubscriptions: Subscription = new Subscription();
  cargoTypes: Array<{ display: string, value: string }> = [
    { display: 'Box', value: 'BOX' },
    { display: 'Pallet', value: 'PALLET' },
    { display: 'Crate', value: 'CRATE' },
    { display: 'Drum', value: 'DRUM' },
    { display: 'Bag', value: 'BAG' },
    { display: 'Sack', value: 'SACK' },
    { display: 'Roll', value: 'ROLL' },
    { display: 'Bundle', value: 'BUNDLE' },
    { display: 'Case', value: 'CASE' },
    { display: 'Bin', value: 'BIN' },
    { display: 'Half Skid', value: 'HALF_SKID' },
    { display: 'Full Skid', value: 'FULL_SKID' },
    { display: 'Container', value: 'CONTAINER' },
    { display: 'Cage', value: 'CAGE' },
    { display: 'Tray', value: 'TRAY' },
    { display: 'Cart', value: 'CART' },
  ];
  weightTypes: Array<{ display: string, value: string }> = [
    { display: 'Lbs', value: 'LBS' },
    { display: 'Kgs', value: 'KGS' }
  ];
  rateTypes: Array<{ display: string, value: string }> = [
    { display: 'Weight', value: 'WEIGHT' },
    { display: 'Volume', value: 'VOLUME' },
    { display: 'Full Skid', value: 'FULL_SKID' },
    { display: 'Half Skid', value: 'HALF_SKID' }
  ];
  shipmentData: any;
  backUrl!: string;
  hasCargoDetails = true;

  constructor(private readonly toastService: ToastService,
    private readonly navController: NavController,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    private readonly route: ActivatedRoute,
    public commonService: CommonService,
  ) {

  }

  ngOnInit(): void {
    const storedData = this.localStorageService.getObject("CARGO_DETAILS");
    this.addCargoDetails = storedData ? CargoDetails.fromResponse(storedData) : new CargoDetails();
  }

  ionViewWillEnter(): void {
    this.onClickValidation = false;

    const storedData = this.localStorageService.getObject("CARGO_DETAILS");
    this.addCargoDetails = storedData ? CargoDetails.fromResponse(storedData) : new CargoDetails();

    this.route.queryParams.subscribe(params => {
      this.shipmentData = params['shipmentData'] || null;
    });

    this.backUrl = `/portal/cargo/listing?shipmentData=${this.shipmentData}`;

    this.addCargoDetails.shipment = this.shipmentData;
    this.addCargoDetails.quantity = 1;

    this.evaluateCargoDetailsVisibility();
  }

  private evaluateCargoDetailsVisibility(): void {
    const d = this.addCargoDetails;
    if (!d.description || !d.cargoType || !d.weightType || !d.rateType || d.quantity === null) {
      this.hasCargoDetails = false;
      setTimeout(() => {
        this.hasCargoDetails = true;
      }, 0);
    } else {
      this.hasCargoDetails = true;
    }
  }

  onWeightChange(): void {
    const weight = this.addCargoDetails.weight ?? 0;

    const type = this.addCargoDetails.weightType?.toUpperCase();
    if (type === 'LBS') {
      this.addCargoDetails.weightInPounds = weight;
    } else {
      this.addCargoDetails.weightInPounds = +(weight * 2.20462).toFixed(2);
    }
  }

  calculateVolume(): void {
    const { length, width, height } = this.addCargoDetails;

    if (length && width && height) {
      this.addCargoDetails.volume = +(length * width * height).toFixed(2);
    } else {
      this.addCargoDetails.volume = null;
    }
  }

  incrementQuantity() {
    this.addCargoDetails.quantity = (this.addCargoDetails.quantity || 1) + 1;
  }

  decrementQuantity() {
    if (this.addCargoDetails.quantity !== null && this.addCargoDetails.quantity > 1) {
      this.addCargoDetails.quantity -= 1;
    }
  }

  async submitProfile(form: any): Promise<void> {
    this.onClickValidation = true;

    if (!form.valid) {
      return;
    }

    this.loadingService.show();
    this.dataService.saveCargoItem(this.addCargoDetails).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();
        this.toastService.show(response.message);

        const data = response.data;
        this.redirectToCargoList(data);
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  redirectToCargoList(data: any) {
    this.navController.navigateForward('/portal/cargo/listing', {
      queryParams: {
        shipmentData: this.shipmentData
      }, animated: true
    });
  }

  cancel() {
    this.navController.navigateBack('/portal/cargo/listing', {
      queryParams: {
        shipmentData: this.shipmentData
      }, animated: true
    });
  }

}
