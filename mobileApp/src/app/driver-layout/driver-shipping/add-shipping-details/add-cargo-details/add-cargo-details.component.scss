 .add-cargo-detail-page {
     --background: white;
     height: 100%;

     .add-cargo-detail-body-section {
         display: inline-block;
         width: 100%;
         height: calc(100vh - 210px);
         padding: 0px 20px 10px 20px !important;
         overflow-y: auto;

         .form-container {
             min-height: 250px;
         }

         .info-text {
             font-size: 17px;
             font-weight: bold;
         }

         // Dropdown styling to match basic-info component
         .site-form-control {
             width: 100%;
             margin-bottom: 10px;

             ion-input,
             ion-select {
                 font-size: 14px;
                 --padding-start: 0px !important;
                 --padding-end: 0px;
                 --padding-top: 3px;
                 --padding-bottom: 0px;
                 font-weight: 500;
                 min-height: 50px !important;
                 width: 100%;
             }

             ion-select::part(icon) {
                 display: none;
             }

             .down-arrow-icon {
                 margin-top: 19px;
                 font-size: 16px;
             }
         }

         // Legacy support for existing ion-select styling
         ion-select {
             font-size: 14px;
             --padding-start: 0px !important;
             --padding-end: 0px;
             --padding-top: 3px;
             --padding-bottom: 0px;
             font-weight: 500;
             min-height: 50px !important;
         }

         ion-select::part(icon) {
             display: none;
         }

         .down-arrow-icon {
             margin-top: 19px;
             font-size: 16px;
         }

         .dimension-fields-container {
             .dimension-row {
                 display: flex;
                 align-items: center;
                 flex-wrap: wrap;
             }

             .dim-item {
                 --border-radius: 16px;
                 flex: 1 1 53px;
                 min-width: 53px;
                 max-width: 70px;
                 --border-width: 1px #D0D0D0 !important;
                 --padding-start: 10px;

                 &.volume {
                     min-width: 82px;
                 }

                 ion-input {
                     font-weight: 600;
                     font-size: 14px;
                     text-align: center;
                 }
             }

             .dim-symbol {
                 font-size: 20px;
                 font-weight: bold;
                 color: #666;
                 display: flex;
                 align-items: center;
                 justify-content: center;
                 padding: 0 4px;
             }
         }

         .quantity-field-container {
             .quantity-input-wrapper {
                 --border-radius: 16px;
                 display: flex;
                 align-items: center;

                 .quantity-left-group {
                     display: flex;
                     align-items: center;

                     .quantity-label {
                         font-weight: 600;
                         margin-right: 8px;
                         white-space: nowrap;
                     }

                     .vertical-separator {
                         width: 1px;
                         height: 20px;
                         background-color: #ccc;
                         margin: 0 8px;
                     }

                     .quantity-display {
                         font-weight: 600;
                         font-size: 14px;
                         width: 40px;
                         text-align: left;
                     }
                 }

                 .quantity-controls {
                     display: flex;
                     flex-direction: column;
                     align-items: center;
                     justify-content: center;

                     .control-icon {
                         font-size: 15px;
                         margin: 4px 0;
                         cursor: pointer;
                     }
                 }
             }
         }

         // Enhanced interactive buttons with ripple effects - 50% width each in row
         .shippment-btn-container {
             display: flex;
             flex-direction: row;
             justify-content: space-between;
             align-items: center;
             gap: 16px;
             margin-top: 30px;
             padding: 0;
             width: 100%;

             .site-button {
                 flex: 1;
                 max-width: calc(50% - 8px);
                 height: 52px;
                 border-radius: 16px;
                 font-weight: 600;
                 font-size: 16px;
                 text-transform: none;
                 box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
                 transition: all 0.3s ease;
                 position: relative;
                 overflow: hidden;
                 user-select: none;

                 &:hover {
                     transform: translateY(-2px);
                     box-shadow: 0 6px 24px rgba(0, 0, 0, 0.15);
                 }

                 &:active {
                     transform: translateY(-1px);
                 }

                 &.ship-cancel-btn {
                     // Enhanced interactive cancel button with black theme
                     position: relative;
                     overflow: hidden;

                     background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
                     color: #000000;
                     box-shadow: none;
                     transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

                     &:hover {
                         transform: translateY(-3px) scale(1.02);
                         box-shadow: none;
                         border-color: #333333;
                         background: linear-gradient(135deg, #f8f9fa 0%, #ecf0f1 100%);
                     }

                     &:active {
                         transform: translateY(-1px) scale(0.98);
                         box-shadow: none;
                     }

                     // Enhanced ripple effect for cancel button
                     ion-ripple-effect {
                         color: rgba(0, 0, 0, 0.3);
                         z-index: 2;
                     }

                     // Animated border effect
                     &::before {
                         content: '';
                         position: absolute;
                         top: 0;
                         left: -100%;
                         width: 100%;
                         height: 100%;
                         background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
                         transition: left 0.5s ease;
                         z-index: 1;
                     }

                     &:hover::before {
                         left: 100%;
                     }

                     // Subtle shake animation on focus
                     &:focus {
                         animation: subtle-shake 0.5s ease-in-out;
                     }
                 }

                 &.ship-submit-btn {
                     // Enhanced interactive Submit button with white text
                     position: relative;
                     overflow: hidden;

                     background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
                     color: #ffffff;
                     box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
                     transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

                     &:hover {
                         transform: translateY(-3px) scale(1.02);
                         box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                         border-color: #1a252f;
                         background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%);
                         color: #ffffff;
                     }

                     &:active {
                         transform: translateY(-1px) scale(0.98);
                         box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
                     }

                     // Enhanced ripple effect for Submit button
                     ion-ripple-effect {
                         color: rgba(255, 255, 255, 0.3);
                         z-index: 2;
                     }

                     // Animated border effect
                     &::before {
                         content: '';
                         position: absolute;
                         top: 0;
                         left: -100%;
                         width: 100%;
                         height: 100%;
                         background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
                         transition: left 0.5s ease;
                         z-index: 1;
                     }

                     &:hover::before {
                         left: 100%;
                     }

                     // Subtle pulse animation on focus
                     &:focus {
                         animation: submit-pulse 0.6s ease-in-out;
                     }

                     // Enhanced text styling
                     span {
                         font-weight: 700 !important;
                         letter-spacing: 0.8px;
                         text-transform: uppercase;
                         position: relative;
                         z-index: 3;
                         transition: all 0.3s ease;
                         text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                     }

                     &:hover span {
                         letter-spacing: 1px;
                         transform: scale(1.05);
                     }
                 }

                 // Ensure ripple effect is visible
                 ion-ripple-effect {
                     z-index: 1;
                     pointer-events: none;
                 }
             }

             // Responsive design - keep buttons in row
             @media (max-width: 480px) {
                 gap: 12px;
                 margin-top: 20px;

                 .site-button {
                     max-width: calc(50% - 6px);
                     height: 48px;
                     font-size: 14px;
                 }
             }

             @media (max-width: 360px) {
                 gap: 8px;

                 .site-button {
                     max-width: calc(50% - 4px);
                     height: 44px;
                     font-size: 13px;
                 }
             }
         }

         // Bold cancel button text with enhanced styling
         .cancel-text {
             font-weight: 700 !important;
             letter-spacing: 0.8px;
             text-transform: uppercase;
             position: relative;
             z-index: 3;
             transition: all 0.3s ease;
             text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

             // Subtle animation on hover
             .ship-cancel-btn:hover & {
                 letter-spacing: 1px;
                 transform: scale(1.05);
             }
         }

         // Keyframe animations for enhanced interactivity
         @keyframes subtle-shake {

             0%,
             100% {
                 transform: translateX(0);
             }

             25% {
                 transform: translateX(-2px);
             }

             75% {
                 transform: translateX(2px);
             }
         }

         @keyframes submit-pulse {

             0%,
             100% {
                 transform: scale(1);
                 box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
             }

             50% {
                 transform: scale(1.05);
                 box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
             }
         }

     }
 }

 .is-invalid {
     --highlight-color-focused: var(--ion-color-danger);
     --border-color: var(--ion-color-danger);
     --border-width: 1px;
     border-radius: 16px;
 }