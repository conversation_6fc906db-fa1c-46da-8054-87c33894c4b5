import { Component, OnInit, OnDestroy } from '@angular/core';
import { NavController } from '@ionic/angular';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { take } from 'rxjs/operators';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { ToastService } from 'src/shared/toast.service';
import { RestResponse } from 'src/shared/auth.model';
import { ShipmentData, DriverShipmentItem } from 'src/modals/shipping-info';
import { CommonService } from 'src/services/common.service';

@Component({
  selector: 'app-driver-shipping',
  templateUrl: './driver-shipping.component.html',
  styleUrls: ['./driver-shipping.component.scss'],
  standalone: false
})
export class DriverShippingComponent implements OnInit, OnDestroy {

  filter: any;
  shippingList: DriverShipmentItem[] = [];
  allShipments: DriverShipmentItem[] = [];
  searchTerm: string = '';
  private subscription: Subscription = new Subscription();

  constructor(
    private readonly navController: NavController,
    private readonly dataService: DataService,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    private readonly route: ActivatedRoute,
    public readonly commonService: CommonService
  ) { }

  ngOnInit() {
    this.initializeFilter();
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  ionViewWillEnter() {
    this.initializeFilter();

    // Check for refresh query parameter - use take(1) to avoid multiple subscriptions
    this.subscription.add(
      this.route.queryParams.pipe(take(1)).subscribe((params: any) => {
        if (params['refresh'] === 'true') {
          console.log('Refreshing shipments due to cargo completion');
          // Force refresh with a slight delay to ensure API has processed the cargo
          setTimeout(() => {
            this.loadShipments();
          }, 500);
          // Clear the query parameter to avoid repeated refreshes
          this.navController.navigateForward('/portal/shipping', { replaceUrl: true });
        } else {
          // Normal load
          this.loadShipments();
        }
      })
    );
  }

  private initializeFilter() {
    this.filter = {} as any;
    this.filter.offset = 1;
    this.filter.tabType = 'PENDING'; // default is PENDING
  }

  onChangeStatusTab(status: string) {
    this.filter.tabType = status;
    this.filterShipmentsByStatus();
  }



  add() {
    this.getRefId();
  }

  getRefId() {
    this.loadingService.show();
    this.subscription.add(
      this.dataService.getRefId().subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          const data = response.data;
          this.navController.navigateForward("/portal/basic/info", {
            queryParams: {
              refIdData: data?.refID,
              reset: true
            }, animated: true
          });
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message);
        }
      })
    );
  }

  // API Integration Methods
  private loadShipments() {
    const apiPayload = {}; // Empty payload as per API specification

    console.log('Loading shipments - API call initiated');
    this.loadingService.show();
    this.subscription.add(
      this.dataService.getShipments(apiPayload).subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          console.log('Shipments loaded successfully');

          const data = response.data;
          if (Array.isArray(data)) {
            this.allShipments = this.transformApiDataToUIModel(data);
            this.filterShipmentsByStatus();
          } else {
            this.toastService.show('Failed to load shipments');
          }
        },
        error: (error) => {
          this.loadingService.hide();
          console.log('Error loading shipments:', error);
          this.toastService.show(error.message || 'An error occurred');
          this.shippingList = [];
        }
      })
    );
  }

  private transformApiDataToUIModel(apiData: ShipmentData[]): DriverShipmentItem[] {
    return apiData.map(item => {
      const transformedItem = {
        id: item.id,
        refId: item.refID,
        paymentType: this.formatPaymentType(item.paymentType),
        from: item.pickupAddressDetail?.city || 'N/A',
        to: item.deliveryAddressDetail?.city || 'N/A',
        etd: this.commonService.formatDate(item.etd),
        status: this.formatStatus(item.status),
        originalStatus: item.status, // Store original API status
        customerName: item.customerUserDetail?.fullName || 'N/A',
        grandTotal: item.grandTotal || 0,
        shipmentType: item.shipmentType,
        step: item.step,
        isCargoAdded: item.isCargoAdded || false
      };

      // Debug logging for cargo status
      console.log(`Shipment ${item.refID}: isCargoAdded = ${item.isCargoAdded}, status = ${item.status}`);

      return transformedItem;
    });
  }

  private filterShipmentsByStatus() {
    if (!this.allShipments || this.allShipments.length === 0) {
      this.shippingList = [];
      return;
    }

    console.log(`Filtering shipments by status: ${this.filter.tabType}`);
    console.log(`Total shipments before filtering: ${this.allShipments.length}`);

    if (this.filter.tabType === 'PENDING') {
      this.shippingList = this.allShipments.filter(shipment => {
        const status = shipment.originalStatus?.toLowerCase() || shipment.status.toLowerCase();
        const isIncluded = status === 'assigned' ||
          status === 'pending' ||
          status === 'in_transit';

        if (status === 'in_transit') {
          console.log(`IN_TRANSIT shipment found: ${shipment.refId} - Status: ${status}`);
        }

        return isIncluded;
      });
    } else if (this.filter.tabType === 'COMPLETED') {
      this.shippingList = this.allShipments.filter(shipment => {
        const status = shipment.originalStatus?.toLowerCase() || shipment.status.toLowerCase();
        return status === 'completed' ||
          status === 'delivered';
      });
    } else {
      this.shippingList = [...this.allShipments];
    }

    console.log(`Shipments after filtering: ${this.shippingList.length}`);
  }

  private formatPaymentType(paymentType: string): string {
    switch (paymentType?.toUpperCase()) {
      case 'PREPAID':
        return 'Prepaid';
      case 'COD':
        return 'COD';
      default:
        return paymentType || 'N/A';
    }
  }

  private formatStatus(status: string): string {
    switch (status?.toUpperCase()) {
      case 'ASSIGNED':
        return 'Pending';
      case 'IN_TRANSIT':
        return 'In Transit';
      case 'COMPLETED':
        return 'Completed';
      case 'PENDING':
        return 'Pending';
      default:
        return status || 'N/A';
    }
  }

  // Dynamic button functionality
  getActionText(item: DriverShipmentItem): string {
    const originalStatus = this.getOriginalStatus(item);
    switch (originalStatus?.toUpperCase()) {
      case 'ASSIGNED':
        return item.isCargoAdded === false ? 'Add Cargo' : 'Start';
      case 'IN_TRANSIT':
        return 'Add POD';
      default:
        return 'View';
    }
  }

  getActionIcon(item: DriverShipmentItem): string {
    const originalStatus = this.getOriginalStatus(item);
    switch (originalStatus?.toUpperCase()) {
      case 'ASSIGNED':
        return item.isCargoAdded === false ? 'cube' : 'play-circle';
      case 'IN_TRANSIT':
        return 'document-attach';
      default:
        return 'eye';
    }
  }

  private getOriginalStatus(item: DriverShipmentItem): string {
    // Use the stored original status from API
    return item.originalStatus || item.status;
  }

  handleShipmentAction(item: DriverShipmentItem): void {
    const originalStatus = this.getOriginalStatus(item);
    switch (originalStatus?.toUpperCase()) {
      case 'ASSIGNED':
        if (item.isCargoAdded === false) {
          this.addCargo(item);
        } else {
          this.startShipment(item);
        }
        break;
      case 'IN_TRANSIT':
        this.addPOD(item);
        break;
      default:
        this.viewShipment(item);
        break;
    }
  }

  private addCargo(item: DriverShipmentItem): void {
    this.navController.navigateForward('/portal/cargo/listing', {
      queryParams: { shipmentData: item.id }
    });
  }

  private startShipment(item: DriverShipmentItem): void {
    this.navController.navigateForward('/portal/start-shipment', {
      queryParams: { shipmentId: item.id, refId: item.refId }
    });
  }

  private addPOD(item: DriverShipmentItem): void {
    this.navController.navigateForward('/portal/pod-management', {
      queryParams: { shipmentId: item.id, refId: item.refId }
    });
  }

  private viewShipment(item: DriverShipmentItem): void {
    // Navigate to shipment details view
    console.log('View shipment:', item);
  }

  // Method to refresh shipments data
  refreshShipments() {
    console.log('Manually refreshing shipments...');
    this.loadShipments();
  }

  // Pull-to-refresh handler
  doRefresh(event: any) {
    console.log('Pull-to-refresh triggered');

    const apiPayload = {}; // Empty payload as per API specification
    this.subscription.add(
      this.dataService.getShipments(apiPayload).subscribe({
        next: (response: RestResponse) => {
          const data = response.data;
          if (Array.isArray(data)) {
            this.allShipments = this.transformApiDataToUIModel(data);
            this.filterShipmentsByStatus();
          } else {
            this.toastService.show('Failed to load shipments');
          }
          event.target.complete();
        },
        error: (error) => {
          this.toastService.show(error.message || 'An error occurred');
          this.shippingList = [];
          event.target.complete();
        }
      })
    );
  }

  // Search functionality
  onSearchChange(event: any) {
    this.searchTerm = event.detail.value || '';
    this.applySearch();
  }

  private applySearch() {
    if (!this.searchTerm.trim()) {
      this.filterShipmentsByStatus();
      return;
    }

    const searchLower = this.searchTerm.toLowerCase();
    this.shippingList = this.allShipments.filter(shipment =>
      shipment.refId.toLowerCase().includes(searchLower) ||
      shipment.customerName.toLowerCase().includes(searchLower) ||
      shipment.from.toLowerCase().includes(searchLower) ||
      shipment.to.toLowerCase().includes(searchLower) ||
      shipment.status.toLowerCase().includes(searchLower)
    );
  }

}
