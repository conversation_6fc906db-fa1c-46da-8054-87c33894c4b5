import { ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { NavController, Platform } from '@ionic/angular';
import { filter, Subscription } from 'rxjs';
import { CommonService } from 'src/services/common.service';


@Component({
  selector: 'app-driver-layout',
  templateUrl: './driver-layout.component.html',
  styleUrls: ['./driver-layout.component.scss'],
  standalone: false
})
export class DriverLayoutComponent implements OnInit, OnDestroy {

  showTabs = true;
  private routeSub!: Subscription;

  constructor(private readonly navController: NavController,
    private readonly router: Router,
    private readonly platform: Platform,
    public readonly commonService: CommonService,
  ) {

  }

  ngOnInit() {
    this.platform.ready().then(() => {
      if (this.platform.is('cordova')) {
        //  this.fcmService.initializeFirebaseToken();
      }
    });
    this.routeSub = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.checkRoute();
      });
  }

  ionViewWillEnter() {
  }

  private checkRoute() {
    const currentRoute = this.router.url;

    const hideTabRoutes = [
      '/portal/dashboard',
      '/portal/shipping',
      '/portal/fuel-receipt',
      '/portal/calendar',
      '/portal/account',
      '/portal/basic/info',
      '/portal/other/info',
      '/portal/pickup/delivery',
      '/portal/delivery/location',
      '/portal/cargo/listing',
      '/portal/add/cargo/details',
      '/portal/special/request',
      '/portal/start-shipment'
    ];

    this.showTabs = hideTabRoutes.some(route => currentRoute.startsWith(route));

    // Set active tab manually
    if (currentRoute.startsWith('/portal/basic/info') || currentRoute.startsWith('/portal/other/info') ||
      currentRoute.startsWith('/portal/pickup/delivery') || currentRoute.startsWith('/portal/delivery/location')
      || currentRoute.startsWith('/portal/cargo/listing') || currentRoute.startsWith('/portal/add/cargo/details')
      || currentRoute.startsWith('/portal/special/request') || currentRoute.startsWith('/portal/start-shipment')) {
      this.commonService.activeTab = 'shipping';
    } else if (currentRoute.startsWith('/portal/shipping')) {
      this.commonService.activeTab = 'shipping';
    } else if (currentRoute.startsWith('/portal/fuel-receipt')) {
      this.commonService.activeTab = 'fuel';
    } else if (currentRoute.startsWith('/portal/calendar')) {
      this.commonService.activeTab = 'calendar';
    } else if (currentRoute.startsWith('/portal/account')) {
      this.commonService.activeTab = 'account';
    } else {
      this.commonService.activeTab = '';
    }
  }

  ngOnDestroy() {
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }
  }

}
