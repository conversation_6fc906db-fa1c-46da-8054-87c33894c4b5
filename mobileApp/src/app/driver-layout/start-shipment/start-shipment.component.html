<ion-content class="start-shipment-page">
  <!-- Header matching parent design -->
  <app-customer-header [innerPage]="true" [headingText]="'Start Shipment'" [rightAction]="false"></app-customer-header>

  <div class="start-shipment-body-section">
    <div class="margin-top-25">
      <span class="info-text">Start Shipment</span>
    </div>
    <div class="form-container">
      <form class="custom-form" #basicForm="ngForm" novalidate>

        <!-- Barcode Section -->
        <div class="margin-top-10 margin-bottom-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{ 'is-invalid': barcode.invalid && onClickValidation }">
            <ion-input required label="Barcode Number" labelPlacement="floating" name="barcode"
              [(ngModel)]="formData.barcodeNo" #barcode="ngModel">
            </ion-input>
            <ion-icon slot="end" src="/assets/images/svg/barcode-outline.svg"></ion-icon>
          </ion-item>
          <app-validation-message [field]="barcode" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please enter barcode number.'">
          </app-validation-message>
        </div>

        <div class="margin-top-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{ 'is-invalid': shipNumber.invalid && onClickValidation }">
            <ion-input readonly label="Shipment Number" labelPlacement="floating" name="shipNumber" required
              [(ngModel)]="formData.shipmentNumber" #shipNumber="ngModel" mode="md">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="shipNumber" [onClickValidation]="onClickValidation">
          </app-validation-message>
        </div>



        <!-- Image Upload Section -->
        <div class="document-upload-container margin-top-20"
          [ngClass]="{'missing-image': showValidationErrors && !requestImage}">
          <img *ngIf="!requestImage" src="/assets/images/svg/attach.svg" alt="Upload Icon" class="upload-icon">
          <div class="upload-text" *ngIf="!requestImage">
            <span class="choose-file-text">Upload a picture of your item</span>
            <span class="upload-multiple-file-text">upload multiple or single file upload (max. 2mb)</span>
            <ion-button class="browse-file-button interactive-button margin-top-20" expand="full" shape="round"
              type="submit" (click)="upload()">
              Browse file
              <ion-ripple-effect></ion-ripple-effect>
            </ion-button>
          </div>
        </div>

        <!-- Image Gallery Below -->
        <div class="image-grid margin-top-30">
          <div class="image-item" *ngFor="let image of uploadedImages" (click)="toggleSelected(image)"
            [class.selected]="image.selected">
            <img [src]="image.url" alt="Uploaded" />
            <div class="icon-container" *ngIf="image.selected">
              <ion-icon class="icon" src="/assets/images/svg/view-icon.svg" (click)="viewImage(image.url)"></ion-icon>
              <ion-icon class="icon" src="/assets/images/svg/delete-icon.svg"
                (click)="onDeleteClick($event, image)"></ion-icon>
            </div>
          </div>
        </div>

        <!-- <div class="upload-section">
        <div class="upload-container" (click)="selectImage()">
          <div class="upload-icon">
            <ion-icon name="camera" size="large"></ion-icon>
          </div>
          <p>Upload a picture of your item</p>
          <p class="upload-subtitle">Click here to browse file</p>
        </div>

        <ion-button expand="block" fill="solid" color="warning" (click)="selectImage()" class="browse-button">
          Browse file
        </ion-button>
      </div> -->

        <div class="shippment-btn-container">
          <ion-button class="margin-top-20 site-button ship-cancel-btn interactive-button" expand="full" shape="round"
            type="submit" (click)="goBack()">
            <span class="cancel-text">Cancel</span>
            <ion-ripple-effect></ion-ripple-effect>
          </ion-button>
          <ion-button class="margin-top-20 site-button ship-submit-btn interactive-button" expand="full" shape="round"
            type="submit" (click)="submitProfile()">
            <span>Start Shipment</span>
            <ion-ripple-effect></ion-ripple-effect>
          </ion-button>
        </div>

      </form>

    </div>

  </div>
</ion-content>