.start-shipment-page {
  --background: white;
  height: 100%;

  .start-shipment-body-section {
    display: inline-block;
    width: 100%;
    height: calc(100vh - 210px);
    padding: 0px 20px 10px 20px !important;
    overflow-y: auto;

    .form-container {
      min-height: 250px;
    }

    .info-text {
      font-size: 17px;
      font-weight: bold;
    }

    .browse-file-button {
      --background: #FFEA00;
      min-height: 48px;
      --border-radius: 22px;
      text-transform: capitalize;
      color: black;
      font-weight: 600;
      font-size: 16px;
    }

    // Interactive button styles
    .interactive-button {
      position: relative;
      overflow: hidden;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      transform: translateZ(0);

      &:hover:not([disabled]) {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &:active:not([disabled]) {
        transform: translateY(0);
        transition: all 0.1s ease;
      }

      &[disabled] {
        opacity: 0.6;
        transform: none;
        box-shadow: none;
      }

      ion-ripple-effect {
        color: rgba(255, 255, 255, 0.3);
      }
    }

    // Enhanced interactive cancel button
    .ship-cancel-btn {
      position: relative;
      overflow: hidden;
      // border: 2px solid #e74c3c;
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
      color: #000000;
      // box-shadow: 0 4px 16px rgba(231, 76, 60, 0.2);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        transform: translateY(-3px) scale(1.02);
        box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
        border-color: #c0392b;
        background: linear-gradient(135deg, #f8f9fa 0%, #ecf0f1 100%);
      }

      &:active {
        transform: translateY(-1px) scale(0.98);
        box-shadow: 0 4px 16px rgba(231, 76, 60, 0.4);
      }

      // Enhanced ripple effect for cancel button
      ion-ripple-effect {
        color: rgba(231, 76, 60, 0.3);
        z-index: 2;
      }

      // Animated border effect
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(231, 76, 60, 0.1), transparent);
        transition: left 0.5s ease;
        z-index: 1;
      }

      &:hover::before {
        left: 100%;
      }

      // Subtle shake animation on focus
      &:focus {
        animation: subtle-shake 0.5s ease-in-out;
      }
    }

    // Keyframe for subtle shake animation
    @keyframes subtle-shake {

      0%,
      100% {
        transform: translateX(0);
      }

      25% {
        transform: translateX(-2px);
      }

      75% {
        transform: translateX(2px);
      }
    }

    // Bold cancel button text with enhanced styling
    .cancel-text {
      font-weight: 700 !important;
      letter-spacing: 0.8px;
      text-transform: uppercase;
      position: relative;
      z-index: 3;
      transition: all 0.3s ease;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

      // Subtle animation on hover
      .ship-cancel-btn:hover & {
        letter-spacing: 1px;
        transform: scale(1.05);
      }
    }

    // Enhanced button container styling
    .shippment-btn-container {
      display: flex;
      gap: 16px;
      margin-top: 30px;
      padding: 0 10px;
      position: relative;

      // Subtle background animation
      &::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, transparent, rgba(255, 234, 0, 0.1), transparent);
        border-radius: 20px;
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: -1;
      }

      &:hover::before {
        opacity: 1;
      }

      .site-button {
        flex: 1;
        height: 52px;
        border-radius: 16px;
        font-weight: 600;
        font-size: 16px;
        text-transform: none;
        position: relative;
        overflow: hidden;
        user-select: none;
      }
    }

    // Enhanced browse button
    .browse-file-button {
      &:hover:not([disabled]) {
        --background: #FFD700;
        transform: translateY(-1px);
      }
    }
  }

}