import { ChangeDetectorRef, Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController, ActionSheetController, AlertController, ModalController } from '@ionic/angular';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { Subscription } from 'rxjs';
import { ToastService } from 'src/shared/toast.service';
import { LoadingService } from 'src/services/loading.service';
import { FullImageComponent } from 'src/shared/full-image/full-image.component';
import { FileCropperComponent } from 'src/shared/file-cropper/file-cropper.component';
import { DataService } from 'src/services/data.service';
import { RestResponse } from 'src/shared/auth.model';

@Component({
  selector: 'app-start-shipment',
  templateUrl: './start-shipment.component.html',
  styleUrls: ['./start-shipment.component.scss'],
  standalone: false
})
export class StartShipmentComponent implements OnInit, OnDestroy {

  shipmentId: string = '';
  refId: string = '';
  onClickValidation!: boolean;
  private subscription: Subscription = new Subscription();

  formData: any;
  showValidationErrors = false;
  requestImage: string | null = null;
  uploadedImages: {
    url: string;
    secureUrl?: string;
    fileName?: string;
    mimeType?: string;
    size?: number;
    originalName?: string;
    path?: string;
    selected?: boolean;
  }[] = [];

  constructor(
    private route: ActivatedRoute,
    private navController: NavController,
    private modalCtrl: ModalController,
    private toastService: ToastService,
    private loadingService: LoadingService,
    private changeDetectorRef: ChangeDetectorRef,
    private readonly dataService: DataService
  ) { }

  ngOnInit() {
    // Clear any previous data
    this.clearFormData();
    this.clearUploadedImages();

    this.subscription.add(
      this.route.queryParams.subscribe(params => {
        this.shipmentId = params['shipmentId'] || '';
        this.refId = params['refId'] || '';
        this.formData.shipmentNumber = this.refId;
      })
    );
  }

  ionViewWillEnter() {
    // Clear any residual data every time the view is entered
    this.clearFormData();
    this.clearUploadedImages();
    this.showValidationErrors = false;
    this.onClickValidation = false;
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  private clearFormData() {
    this.formData = {
      shipmentNumber: this.refId || '',
      barcodeNo: '',
      isSecured: false
    };
  }

  private clearUploadedImages() {
    this.uploadedImages = [];
    this.requestImage = null;
  }

  goBack() {
    this.navController.back();
  }

  async upload() {
    try {
      const response = await Camera.getPhoto({
        quality: 50,
        allowEditing: false,
        resultType: CameraResultType.Base64,
        source: CameraSource.Prompt, // Prompt user to select from camera or files
      });
      if (response.base64String) {
        await this.processCropper(response.base64String);
      }
    } catch (error) {
      //  this.toastService.show("Something went wrong while uploading profile picture.");
    }
  }

  async processCropper(base64Image: string) {
    const modal = await this.modalCtrl.create({
      component: FileCropperComponent,
      componentProps: {
        file: { base64String: base64Image }
      },
      cssClass: "cropper-modal"
    });
    await modal.present();
    const { data, role } = await modal.onWillDismiss();
    if (role !== 'confirm') {
      this.toastService.show("Sorry, profile picture uploading has been cancelled.");
      return;
    }
    if (data && data.croppedFile) {
      await this.uploadImage(data.croppedFile);
    } else {
      this.toastService.show("No file selected after cropping.");
    }
  }

  async uploadImage(blob: Blob): Promise<void> {
    if (!blob) {
      this.toastService.show('No file selected. Please choose an image to upload.');
      return;
    }

    const file = new File([blob], 'cropped-image.png', { type: 'image/png' }); // Convert Blob to File

    const formData = new FormData();
    formData.append('file', file, file.name);

    this.loadingService.show(); // Show loading indicator

    this.subscription.add(
      this.dataService.uploadFile(formData).subscribe({
        next: (response: any) => {
          this.loadingService.hide();
          this.handleUploadResponse(response);
        },
        error: (error) => {
          this.loadingService.hide();
          this.toastService.show(error.message || 'An error occurred while uploading the file');
        }
      })
    );
  }

  private handleUploadResponse(response: any): void {
    const attachments = response?.data;

    if (Array.isArray(attachments) && attachments.length > 0) {
      attachments.forEach((attachment: any) => {
        // Use secureUrl for display but preserve clean path for API
        const displayUrl = attachment.secureUrl || attachment.path || attachment.fileName;
        this.uploadedImages.push({
          url: displayUrl, // For display purposes
          secureUrl: attachment.secureUrl || '',
          fileName: attachment.filename || '',
          mimeType: attachment.mimeType || 'image/png',
          size: attachment.size || 0,
          originalName: attachment.originalName || '',
          path: attachment.path || '' // Store clean path separately
        });
      });
      this.changeDetectorRef.detectChanges();
      this.toastService.show('File uploaded successfully.');
    } else {
      this.toastService.show('Failed to upload file.');
    }
  }

  toggleSelected(selectedImage: { url: string, selected?: boolean }) {
    this.uploadedImages.forEach(image => {
      if (image === selectedImage) {
        image.selected = !image.selected; // Toggle current image
      } else {
        image.selected = false; // Deselect others
      }
    });
  }

  onDeleteClick(event: Event, image: { url: string }) {
    event.stopPropagation(); // Prevent parent (click) from firing
    this.removeImage(image);
  }

  removeImage(image: { url: string }) {
    this.uploadedImages = this.uploadedImages.filter(img => img.url !== image.url);
  }

  async viewImage(imageUrl: string) {
    const modal = await this.modalCtrl.create({
      component: FullImageComponent,
      componentProps: { imageUrl },
      cssClass: 'full-image-modal'
    });
    await modal.present();
    const { data, role } = await modal.onWillDismiss();
    if (role === 'remove' && data) {
      this.removeImage({ url: data }); // use existing method
    }
  }

  async submitProfile(): Promise<void> {
    // Validate required fields
    if (!this.formData.barcodeNo || this.formData.barcodeNo.trim() === '') {
      this.toastService.show('Please enter a barcode number.');
      return;
    }

    if (this.uploadedImages.length === 0) {
      this.toastService.show('Please upload at least one image before submitting.');
      return;
    }

    // Prepare the API payload
    const payload = {
      id: this.shipmentId,
      barcodeNo: this.formData.barcodeNo.trim(),
      refId: this.refId,
      isSecured: true, // Always true as per requirement
      popImages: this.generatePopImages()
    };


    this.loadingService.show();
    this.subscription.add(
      this.dataService.startShipment(payload).subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();

          if (response.status === 200) {
            this.toastService.show('Shipment started successfully!');
            this.navController.navigateForward('/portal/shipping', {
              queryParams: { refresh: 'true' }
            });
          } else {
            this.toastService.show(response.message || 'Failed to start shipment');
          }
        },
        error: (error) => {
          this.loadingService.hide();
          this.toastService.show(error.message || 'An error occurred while starting the shipment');
        }
      })
    );
  }

  private generatePopImages() {
    return this.uploadedImages.map((image) => ({
      filename: image.fileName || '',
      mimeType: image.mimeType || 'image/png',
      size: image.size || 0,
      path: image.path || image.url, // Use clean path if available, fallback to url
      originalName: image.originalName || '',
      secureUrl: image.secureUrl || ''
    }));
  }

}
