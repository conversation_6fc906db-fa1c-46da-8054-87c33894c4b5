<ion-content class="profile-page">
  <app-customer-header [innerPage]="false" [headingText]="'Profile Management'"
    [rightAction]="true"></app-customer-header>

  <div class="profile-page-body-section">
    <div class="margin-top-25">
      <span class="profile-text">Enter Profile Details</span>
    </div>
    <div class="form-container">
      <form class="custom-form" #profileForm="ngForm" novalidate (ngSubmit)="submitProfile(profileForm.form)">

        <div class="margin-top-20 margin-bottom-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{ 'is-invalid': firstName.invalid && onClickValidation }">
            <ion-input label="First Name" labelPlacement="floating" name="firstName" required
              [(ngModel)]="profileData.firstName" #firstName="ngModel" maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$"
              mode="md">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="firstName" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Only alphabetic characters are allowed.'">
          </app-validation-message>
        </div>

        <div class="margin-top-10 margin-bottom-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid': lastName.invalid && onClickValidation}">
            <ion-input name="lastName" #lastName="ngModel" [(ngModel)]="profileData.lastName" required="required"
              maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="Last Name" labelPlacement="floating">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="lastName" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Only alphabetic characters are allowed.'">
          </app-validation-message>
        </div>

        <!-- Phone Number -->
        <!-- <div class="margin-top-10 margin-bottom-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':userPhone.invalid && onClickValidation}">
            <img width="30" height="30" [attr.alt]="countryIsoCode" [src]="countryIsoCode" slot="start"
              [style.border-radius.%]="50" *ngIf="countryIsoCode!==''" />
            <input class="custom-mobile-input" inputmode="tel" [attr.pattern]="pattern" [maskito]="mask"
              required="required" name="userPhone" #userPhone="ngModel" [(ngModel)]="profileData.phoneNumber"
              placeholder="Phone Number"
              (ngModelChange)="profileData.phoneNumber = $event.trim(); onPhoneChange(userPhone)" />
          </ion-item>
          <app-validation-message [field]="userPhone" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid contact number.'">
          </app-validation-message>
        </div> -->

        <div class="margin-top-10 margin-bottom-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':userPhone.invalid && onClickValidation}">
            <ion-icon src="/assets/images/svg/canada-flag-icon.svg" slot="start" class="start-icon"></ion-icon>
            <ion-input inputmode="tel" required name="userPhone" #userPhone="ngModel"
              [(ngModel)]="profileData.phoneNumber" placeholder="Phone Number"
              (ionInput)="formatPhoneNumber($event, userPhone)">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="userPhone" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid contact number.'">
          </app-validation-message>
        </div>

        <!-- Email (Disabled) -->
        <div class="margin-top-10 margin-bottom-10">
          <ion-item class="site-form-control disabled-field" lines="none">
            <ion-input label="Email" labelPlacement="floating" name="email" type="email" [(ngModel)]="profileData.email"
              #email="ngModel" readonly="true" disabled="true">
            </ion-input>
          </ion-item>
        </div>

        <div class="shippment-btn-container">
          <ion-button class="margin-top-20 site-button ship-cancel-btn" expand="full" shape="round" type="button"
            (click)="cancel()">
            <span>Cancel</span>
          </ion-button>
          <ion-button class="margin-top-20 site-button ship-submit-btn" expand="full" shape="round" type="submit">
            <span>Submit</span>
          </ion-button>
        </div>
      </form>
    </div>

  </div>
</ion-content>