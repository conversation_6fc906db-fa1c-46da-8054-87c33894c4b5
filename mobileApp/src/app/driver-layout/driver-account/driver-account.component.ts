import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { CommonService } from 'src/services/common.service';
import { UserProfileService } from 'src/services/user-profile.service';
import { LoadingService } from 'src/services/loading.service';
import { ToastService } from 'src/shared/toast.service';
import { UserProfileModel, UserProfileResponse } from 'src/modals/user-profile-response';
import { RestResponse } from 'src/shared/auth.model';
import mask from 'src/shared/phone-number.mask';
import { maskitoGetCountryFromNumber } from '@maskito/phone';
import metadata from 'libphonenumber-js/min/metadata';
import { isValidPhoneNumber } from 'libphonenumber-js/core';
import { NgModel } from '@angular/forms';

@Component({
  selector: 'app-driver-account',
  templateUrl: './driver-account.component.html',
  styleUrls: ['./driver-account.component.scss'],
  standalone: false
})
export class DriverAccountComponent implements OnInit, OnDestroy {

  onClickValidation!: boolean;
  profileData: UserProfileModel = new UserProfileModel();
  activeSubscriptions: Subscription = new Subscription();
  isApple: boolean = false;
  code: string = "";
  protected readonly mask = mask;

  constructor(
    private readonly navController: NavController,
    private readonly userProfileService: UserProfileService,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    public commonService: CommonService,
  ) {

  }

  ngOnInit(): void {
    this.resetForm();
  }

  ionViewWillEnter(): void {
    // Always load fresh profile data when screen is entered
    this.loadUserProfile();
  }

  resetForm(): void {
    this.profileData = new UserProfileModel();
    this.onClickValidation = false;
  }

  // Load user profile from API (always fresh data)
  loadUserProfile(): void {
    this.loadingService.show();

    this.activeSubscriptions.add(
      this.userProfileService.refreshUserProfile().subscribe({
        next: (response: UserProfileResponse) => {
          this.loadingService.hide();
          if (response.status === 200 && response.data) {
            this.profileData = UserProfileModel.fromApiResponse(response.data);
            console.log('Profile loaded successfully:', this.profileData);
          } else {
            this.toastService.show('Failed to load profile data');
          }
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message || 'Failed to load profile');
          console.error('Error loading profile:', error);
        }
      })
    );
  }

  // protected get pattern(): string {
  //   return this.isApple ? '+[0-9-]{1,20}' : '';
  // }

  // onPhoneChange(userPhone: NgModel): void {
  //   setTimeout(() => {
  //     const phone = this.profileData?.phoneNumber?.trim() || '';

  //     const currentErrors = userPhone.control.errors || {};

  //     if (phone === '') {
  //       userPhone.control.setErrors({ required: true });
  //     } else {
  //       const isValid = isValidPhoneNumber(phone, metadata);

  //       if (isValid) {
  //         delete currentErrors['pattern'];
  //         delete currentErrors['required'];
  //         userPhone.control.setErrors(Object.keys(currentErrors).length ? currentErrors : null);
  //       } else {
  //         userPhone.control.setErrors({ pattern: true });
  //       }
  //     }
  //   });
  // }

  protected get countryIsoCode(): string {
    const phone = this.profileData?.phoneNumber;
    if (!phone || phone.trim() === '') {
      return '';
    }

    const code = maskitoGetCountryFromNumber(phone, metadata) ?? '';
    //  return code ? `/assets/images/icons/flags/${code.toLowerCase()}.png` : '';
    return code ? `/assets/images/icons/flags/ca.png` : '';
  }

  protected get pattern(): string {
    return '^\\+1\\d{10}$'; // +1 followed by exactly 10 digits
  }

  formatPhoneNumber(event: any, userPhone: NgModel): void {
    const input = event.target;
    let value: string = input.value || '';

    // Remove all non-digit characters
    const digits = value.replace(/\D/g, '').slice(0, 10); // Max 10 digits

    let formatted = digits;
    if (digits.length > 6) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3, 6)} ${digits.slice(6)}`;
    } else if (digits.length > 3) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3)}`;
    }

    this.profileData.phoneNumber = formatted;
    input.value = formatted;

    if (digits.length < 10) {
      userPhone.control.setErrors({ required: true });
    } else {
      userPhone.control.setErrors(null);
    }
  }

  async submitProfile(form: any): Promise<void> {
    this.onClickValidation = true;

    if (!form.valid || !this.profileData.isValid(form)) {
      return;
    }

    this.loadingService.show();

    this.activeSubscriptions.add(
      this.userProfileService.updateUserProfile(this.profileData).subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          if (response.status === 200) {
            this.toastService.show('Profile updated successfully!');
            // Profile data is automatically updated in global state by the service
            console.log('Profile updated and global state refreshed');
            this.navController.navigateForward("/portal/dashboard", { animated: true });
          } else {
            this.toastService.show(response.message || 'Failed to update profile');
          }
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message || 'Failed to update profile');
          console.error('Error updating profile:', error);
        }
      })
    );
  }

  cancel() {
    this.navController.navigateRoot("/portal/dashboard", { animated: true });
  }

  ngOnDestroy(): void {
    this.activeSubscriptions.unsubscribe();
  }

}
