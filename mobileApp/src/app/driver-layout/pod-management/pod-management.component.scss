.pod-management-page {
  --background: #f8f9fa;

  .pod-management-body-section {
    display: inline-block;
    width: 100%;
    height: calc(100vh - 210px);
    padding: 0px 20px 10px 20px !important;
    overflow-y: auto;

    .form-container {
      min-height: 250px;
    }

    .info-text {
      font-size: 17px;
      font-weight: bold;
    }

    .browse-file-button {
      --background: #FFEA00;
      min-height: 48px;
      --border-radius: 22px;
      text-transform: capitalize;
      color: black;
      font-weight: 600;
      font-size: 16px;
    }

    .signature-section {
      .signature-label {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 8px;
        padding-left: 4px;
      }
    }

    // Interactive button styles
    .interactive-button {
      position: relative;
      overflow: hidden;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      transform: translateZ(0);

      &:hover:not([disabled]) {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &:active:not([disabled]) {
        transform: translateY(0);
        transition: all 0.1s ease;
      }

      &[disabled] {
        opacity: 0.6;
        transform: none;
        box-shadow: none;
      }

      ion-ripple-effect {
        color: rgba(255, 255, 255, 0.3);
      }
    }

    // Bold cancel button text
    .cancel-text {
      font-weight: 700 !important;
      letter-spacing: 0.5px;
    }

    // Enhanced browse button
    .browse-file-button {
      &:hover:not([disabled]) {
        --background: #FFD700;
        transform: translateY(-1px);
      }
    }
  }
}