<ion-content class="calendar-tab-page">
  <app-customer-header [innerPage]="false" [headingText]="'Calendar view'" [rightAction]="false"></app-customer-header>

  <div class="calendar-tab-body-section">

    <div class="calendar-header">
      <h2>{{ currentDate | date: 'MMMM, y' }}</h2>
      <div class="calendar-icon-header-container">
        <div class="calendar-icon-header">
          <ion-icon name="chevron-back-outline" (click)="goToPreviousMonth()"></ion-icon>
        </div>
        <div class="calendar-icon-header">
          <ion-icon name="chevron-forward-outline" (click)="goToNextMonth()"></ion-icon>
        </div>
      </div>
    </div>

    <div class="calendar-grid">
      <div class="day-header" *ngFor="let day of ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']">
        {{ day }}
      </div>

      <div class="day-cell" *ngFor="let week of weeks">
        <ng-container *ngFor="let day of week">
          <div class="date-box" [class.inactive]="!isSameMonth(day)" [class.selected]="isSelected(day)"
            (click)="selectDate(day)">
            <div class="date">{{ day.getDate() }}</div>
            <div class="status-dots">
              <span *ngFor="let status of getStatus(day)" [class]="status + '-dot'"></span>
            </div>
          </div>
        </ng-container>
      </div>
    </div>

    <div class="shipment-schedule-container">
      <span class="schedule-text margin-top-25 margin-left-5">Schedule</span>
      <div class="shipment-list margin-top-20" *ngIf="selectedDateShipments.length > 0; else noShipments">
        <div class="shipment-item" *ngFor="let shipment of selectedDateShipments">
          <div class="icon-circle" [style.background-color]="getIconColor(shipment.statusColor)">
            <ion-icon name="ellipse"></ion-icon>
          </div>
          <div class="shipment-details">
            <div class="shipment-title">{{ shipment.title }}</div>
            <div class="shipment-time">{{ shipment.time }}</div>
            <div class="shipment-status">{{ shipment.status }} • {{ shipment.customerName }}</div>
          </div>
        </div>
      </div>

      <ng-template #noShipments>
        <div class="no-shipments-message margin-top-20">
          <p>No shipments scheduled for {{ selectedDate | date: 'MMM dd, yyyy' }}</p>
        </div>
      </ng-template>
    </div>

  </div>
</ion-content>