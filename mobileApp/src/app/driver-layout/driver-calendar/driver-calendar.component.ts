import { Component, OnDestroy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { ToastService } from 'src/shared/toast.service';
import { RestResponse } from 'src/shared/auth.model';
import { ShipmentData, DriverShipmentItem } from 'src/modals/shipping-info';
import { CommonService } from 'src/services/common.service';

// Shipment status options with color mapping
const SHIPMENT_STATUS_OPTIONS = [
  { id: 'NEW', name: 'New', color: 'red' },
  { id: 'ASSIGNED', name: 'Assigned', color: 'red' },
  { id: 'IN_TRANSIT', name: 'In Transit', color: 'yellow' },
  { id: 'COMPLETED', name: 'Completed', color: 'green' },
  { id: 'DELIVERED', name: 'Delivered', color: 'green' },
  { id: 'CANCELLED', name: 'Cancelled', color: 'green' }
];

interface CalendarShipment {
  id: string;
  refId: string;
  title: string;
  time: string;
  status: string;
  statusColor: string;
  customerName: string;
  from: string;
  to: string;
  etd: string;
}

// Extended interface for calendar component
interface CalendarDriverShipmentItem extends DriverShipmentItem {
  formattedEtd?: string;
}

@Component({
  selector: 'app-driver-calendar',
  templateUrl: './driver-calendar.component.html',
  styleUrls: ['./driver-calendar.component.scss'],
  standalone: false
})
export class DriverCalendarComponent implements OnInit, OnDestroy {

  currentDate: Date = new Date();
  weeks: Date[][] = [];
  selectedDate: Date | null = null;

  // Shipment data
  allShipments: DriverShipmentItem[] = [];
  shipmentsByDate: { [key: string]: CalendarShipment[] } = {};
  selectedDateShipments: CalendarShipment[] = [];

  private subscription = new Subscription();

  constructor(
    private readonly dataService: DataService,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    private readonly commonService: CommonService
  ) {
    this.generateCalendar();
  }

  ngOnInit(): void {
    this.selectedDate = new Date(); // Select today by default
    this.loadShipments();
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  ionViewWillEnter() {
    this.currentDate = new Date();
    this.selectedDate = new Date();
    this.generateCalendar();
    this.loadShipments();
  }

  // Load shipments from API
  loadShipments(): void {
    console.log('Loading shipments for calendar...');
    this.loadingService.show();

    const apiPayload = {}; // Empty payload as per API specification
    this.subscription.add(
      this.dataService.getShipments(apiPayload).subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          console.log('🚀 Shipments API Response:', response);
          console.log('📦 Raw shipments data:', response.data);

          const data = response.data;
          if (Array.isArray(data)) {
            console.log(`📊 Found ${data.length} shipments from API`);
            this.allShipments = this.transformApiDataToUIModel(data);
            console.log('🔄 Transformed shipments:', this.allShipments);
            this.organizeShipmentsByDate();
            console.log('📅 Shipments organized by date:', this.shipmentsByDate);
            this.updateSelectedDateShipments();
            console.log('📋 Selected date shipments:', this.selectedDateShipments);
          } else {
            console.error('❌ API response data is not an array:', data);
            this.toastService.show('Failed to load shipments');
          }
        },
        error: (error) => {
          this.loadingService.hide();
          console.error('❌ Error loading shipments for calendar:', error);
          this.toastService.show(error.message || 'An error occurred');
        }
      })
    );
  }

  // Transform API data to UI model
  private transformApiDataToUIModel(apiData: ShipmentData[]): any[] {
    console.log('🔄 Starting transformation of API data...');
    console.log('📊 Raw API data sample:', apiData.slice(0, 2)); // Show first 2 items

    return apiData.map((item, index) => {
      console.log(`📦 Processing item ${index + 1}/${apiData.length}:`, {
        id: item.id,
        refID: item.refID,
        etd: item.etd,
        status: item.status,
        fullItem: item
      });

      const transformedItem = {
        id: item.id,
        refId: item.refID,
        paymentType: this.formatPaymentType(item.paymentType),
        from: item.pickupAddressDetail?.city || 'N/A',
        to: item.deliveryAddressDetail?.city || 'N/A',
        etd: item.etd || '', // Keep raw ETD for parsing
        formattedEtd: this.commonService.formatDate(item.etd), // Store formatted version
        status: this.formatStatus(item.status),
        originalStatus: item.status,
        customerName: item.customerUserDetail?.fullName || 'N/A',
        grandTotal: item.grandTotal || 0,
        shipmentType: item.shipmentType,
        step: item.step,
        isCargoAdded: item.isCargoAdded || false
      };

      console.log(`✅ Transformed shipment ${item.refID}:`, transformedItem);

      return transformedItem;
    });
  }

  // Helper methods for formatting
  private formatPaymentType(paymentType: string): string {
    switch (paymentType?.toUpperCase()) {
      case 'PREPAID':
        return 'Prepaid';
      case 'COD':
        return 'COD';
      default:
        return paymentType || 'N/A';
    }
  }

  private formatStatus(status: string): string {
    switch (status?.toUpperCase()) {
      case 'ASSIGNED':
        return 'Assigned';
      case 'IN_TRANSIT':
        return 'In Transit';
      case 'COMPLETED':
        return 'Completed';
      case 'DELIVERED':
        return 'Delivered';
      case 'NEW':
        return 'New';
      case 'CANCELLED':
        return 'Cancelled';
      default:
        return status || 'N/A';
    }
  }

  // Organize shipments by date
  private organizeShipmentsByDate(): void {
    console.log('🗓️ Organizing shipments by date...');
    this.shipmentsByDate = {};

    this.allShipments.forEach((shipment, index) => {
      console.log(`📦 Processing shipment ${index + 1}:`, shipment);
      console.log(`📅 ETD value: "${shipment.etd}" (type: ${typeof shipment.etd})`);

      if (shipment.etd && shipment.etd.trim() !== '') {
        console.log(`📅 ETD found: "${shipment.etd}"`);
        // Parse the ETD date
        const etdDate = this.parseETDDate(shipment.etd);
        console.log(`🔍 Parsed date:`, etdDate);

        if (etdDate) {
          const dateKey = this.format(etdDate);
          console.log(`🔑 Date key: "${dateKey}"`);

          if (!this.shipmentsByDate[dateKey]) {
            this.shipmentsByDate[dateKey] = [];
          }

          const calendarShipment: CalendarShipment = {
            id: shipment.id,
            refId: shipment.refId,
            title: this.getShipmentTitle(shipment),
            time: this.getShipmentTime(shipment),
            status: shipment.status,
            statusColor: this.getStatusColor(shipment.originalStatus || shipment.status),
            customerName: shipment.customerName,
            from: shipment.from,
            to: shipment.to,
            etd: shipment.etd
          };

          this.shipmentsByDate[dateKey].push(calendarShipment);
          console.log(`✅ Added shipment to date ${dateKey}:`, calendarShipment);
        } else {
          console.warn(`⚠️ Could not parse ETD date: "${shipment.etd}"`);
        }
      } else {
        console.warn(`⚠️ No ETD found for shipment (ETD is null, undefined, or empty):`, {
          shipment: shipment,
          etd: shipment.etd,
          etdType: typeof shipment.etd
        });
      }
    });

    console.log('📊 Final shipmentsByDate:', this.shipmentsByDate);

    // Summary of dates with shipments
    const datesWithShipments = Object.keys(this.shipmentsByDate);
    console.log(`📈 Summary: ${datesWithShipments.length} dates have shipments`);
    datesWithShipments.forEach(dateKey => {
      const shipments = this.shipmentsByDate[dateKey];
      const statusCounts = shipments.reduce((acc, ship) => {
        acc[ship.statusColor] = (acc[ship.statusColor] || 0) + 1;
        return acc;
      }, {} as any);
      console.log(`📅 ${dateKey}: ${shipments.length} shipments`, statusCounts);
    });
  }

  // Parse ETD date from various formats
  private parseETDDate(etd: string): Date | null {
    console.log(`🔍 Parsing ETD: "${etd}" (length: ${etd.length})`);

    if (!etd || etd.trim() === '') {
      console.warn('⚠️ ETD is empty or null');
      return null;
    }

    try {
      // Clean the input
      const cleanEtd = etd.trim();
      console.log(`🧹 Cleaned ETD: "${cleanEtd}"`);

      // Try direct ISO parsing first (most common API format)
      console.log('🔄 Trying direct ISO date parsing...');
      let date = new Date(cleanEtd);
      if (!isNaN(date.getTime())) {
        console.log('✅ ISO parsing successful:', date);
        return date;
      }

      // Handle dash-separated dates
      if (cleanEtd.includes('-')) {
        console.log('📅 Contains dash, parsing as dash-separated date');
        // Remove time part if exists (e.g., "2025-04-15T10:30:00" or "25-03-25, 11:30pm")
        const parts = cleanEtd.split(/[T,]/)[0].trim();
        console.log('📝 Date part after removing time:', parts);
        const dateParts = parts.split('-');
        console.log('🔢 Date parts:', dateParts);

        if (dateParts.length === 3) {
          let year, month, day;

          if (dateParts[0].length === 4) {
            // Format: "2025-03-25"
            console.log('📅 Format detected: YYYY-MM-DD');
            year = parseInt(dateParts[0]);
            month = parseInt(dateParts[1]) - 1; // Month is 0-indexed
            day = parseInt(dateParts[2]);
          } else if (dateParts[2].length === 4) {
            // Format: "25-03-2025" (DD-MM-YYYY)
            console.log('📅 Format detected: DD-MM-YYYY');
            day = parseInt(dateParts[0]);
            month = parseInt(dateParts[1]) - 1;
            year = parseInt(dateParts[2]);
          } else {
            // Format: "25-03-25" (assuming DD-MM-YY)
            console.log('📅 Format detected: DD-MM-YY');
            day = parseInt(dateParts[0]);
            month = parseInt(dateParts[1]) - 1;
            year = 2000 + parseInt(dateParts[2]);
          }

          console.log(`📊 Parsed values: Year=${year}, Month=${month}, Day=${day}`);
          const parsedDate = new Date(year, month, day);
          console.log('✅ Final parsed date:', parsedDate);
          return parsedDate;
        }
      }

      // Handle slash-separated dates (e.g., "04/15/2025")
      if (cleanEtd.includes('/')) {
        console.log('� Contains slash, parsing as slash-separated date');
        const parts = cleanEtd.split(/[T,]/)[0].trim();
        const dateParts = parts.split('/');
        console.log('🔢 Date parts:', dateParts);

        if (dateParts.length === 3) {
          // Assume MM/DD/YYYY format
          const month = parseInt(dateParts[0]) - 1;
          const day = parseInt(dateParts[1]);
          const year = parseInt(dateParts[2]);

          console.log(`📊 Parsed values: Year=${year}, Month=${month}, Day=${day}`);
          const parsedDate = new Date(year, month, day);
          console.log('✅ Final parsed date:', parsedDate);
          return parsedDate;
        }
      }

      console.error('❌ No matching date format found for:', cleanEtd);
      return null;
    } catch (error) {
      console.error('❌ Failed to parse ETD date:', etd, error);
      return null;
    }
  }

  // Get shipment title based on status
  private getShipmentTitle(shipment: DriverShipmentItem): string {
    const status = shipment.originalStatus || shipment.status;
    switch (status.toUpperCase()) {
      case 'NEW':
      case 'ASSIGNED':
        return 'Shipment Pickup';
      case 'IN_TRANSIT':
        return 'Shipment In Transit';
      case 'COMPLETED':
      case 'DELIVERED':
        return 'Shipment Delivered';
      default:
        return 'Shipment';
    }
  }

  // Get shipment time display
  private getShipmentTime(shipment: DriverShipmentItem): string {
    return `${shipment.refId} • ${shipment.from} → ${shipment.to}`;
  }

  // Get status color for dots
  private getStatusColor(status: string): string {
    const statusOption = SHIPMENT_STATUS_OPTIONS.find(
      option => option.id.toUpperCase() === status.toUpperCase()
    );
    return statusOption ? statusOption.color : 'green';
  }

  // Update shipments for selected date
  private updateSelectedDateShipments(): void {
    if (this.selectedDate) {
      const dateKey = this.format(this.selectedDate);
      console.log(`📅 Selected date: ${this.selectedDate}`);
      console.log(`🔑 Looking for shipments with key: "${dateKey}"`);
      console.log('📊 Available date keys:', Object.keys(this.shipmentsByDate));

      this.selectedDateShipments = this.shipmentsByDate[dateKey] || [];
      console.log(`📋 Found ${this.selectedDateShipments.length} shipments for selected date:`, this.selectedDateShipments);
    } else {
      console.log('⚠️ No date selected');
      this.selectedDateShipments = [];
    }
  }

  // Get icon color for shipment items
  getIconColor(statusColor: string): string {
    switch (statusColor) {
      case 'red':
        return '#f44336';
      case 'yellow':
        return '#ffc107';
      case 'green':
        return '#4caf50';
      default:
        return '#000000';
    }
  }

  generateCalendar() {
    const firstDay = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);
    const lastDay = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0);

    const weeks: Date[][] = [];
    let currentWeek: Date[] = [];

    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - startDate.getDay());

    const endDate = new Date(lastDay);
    endDate.setDate(endDate.getDate() + (6 - endDate.getDay()));

    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      currentWeek.push(new Date(d));
      if (currentWeek.length === 7) {
        weeks.push(currentWeek);
        currentWeek = [];
      }
    }

    this.weeks = weeks;
  }

  format(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  // Get status dots for a specific date
  getStatus(date: Date): string[] {
    const dateKey = this.format(date);
    const dayShipments = this.shipmentsByDate[dateKey] || [];

    if (dayShipments.length === 0) {
      return [];
    }

    // Get unique status colors for the day
    const statusColors = new Set<string>();
    dayShipments.forEach(shipment => {
      statusColors.add(shipment.statusColor);
    });

    const uniqueColors = Array.from(statusColors);

    // Debug logging for dates with shipments
    if (uniqueColors.length > 0) {
      console.log(`📅 Date ${dateKey} has ${dayShipments.length} shipments with colors:`, uniqueColors);
      console.log(`📦 Shipments:`, dayShipments.map(s => `${s.refId} (${s.statusColor})`));
    }

    return uniqueColors;
  }

  isSameMonth(date: Date): boolean {
    return date.getMonth() === this.currentDate.getMonth();
  }

  isSelected(date: Date): boolean {
    return !!this.selectedDate && date.toDateString() === this.selectedDate?.toDateString();
  }

  selectDate(date: Date) {
    this.selectedDate = new Date(date);
    this.updateSelectedDateShipments();
  }

  goToPreviousMonth(): void {
    const newDate = new Date(this.currentDate);
    newDate.setMonth(this.currentDate.getMonth() - 1);
    this.currentDate = newDate;
    this.generateCalendar();
    this.organizeShipmentsByDate(); // Re-organize shipments for new month
    this.updateSelectedDateShipments();
  }

  goToNextMonth(): void {
    const newDate = new Date(this.currentDate);
    newDate.setMonth(this.currentDate.getMonth() + 1);
    this.currentDate = newDate;
    this.generateCalendar();
    this.organizeShipmentsByDate(); // Re-organize shipments for new month
    this.updateSelectedDateShipments();
  }

}
