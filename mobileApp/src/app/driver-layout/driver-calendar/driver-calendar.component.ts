import { Component, OnDestroy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { ToastService } from 'src/shared/toast.service';
import { RestResponse } from 'src/shared/auth.model';
import { ShipmentData, DriverShipmentItem } from 'src/modals/shipping-info';
import { CommonService } from 'src/services/common.service';

// Shipment status options with color mapping
const SHIPMENT_STATUS_OPTIONS = [
  { id: 'NEW', name: 'New', color: 'red' },
  { id: 'ASSIGNED', name: 'Assigned', color: 'red' },
  { id: 'IN_TRANSIT', name: 'In Transit', color: 'yellow' },
  { id: 'COMPLETED', name: 'Completed', color: 'green' },
  { id: 'DELIVERED', name: 'Delivered', color: 'green' },
  { id: 'CANCELLED', name: 'Cancelled', color: 'green' }
];

interface CalendarShipment {
  id: string;
  refId: string;
  title: string;
  time: string;
  status: string;
  statusColor: string;
  customerName: string;
  from: string;
  to: string;
  etd: string;
}

@Component({
  selector: 'app-driver-calendar',
  templateUrl: './driver-calendar.component.html',
  styleUrls: ['./driver-calendar.component.scss'],
  standalone: false
})
export class DriverCalendarComponent implements OnInit, OnDestroy {

  currentDate: Date = new Date();
  weeks: Date[][] = [];
  selectedDate: Date | null = null;

  // Shipment data
  allShipments: DriverShipmentItem[] = [];
  shipmentsByDate: { [key: string]: CalendarShipment[] } = {};
  selectedDateShipments: CalendarShipment[] = [];

  private subscription = new Subscription();

  constructor(
    private readonly dataService: DataService,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    private readonly commonService: CommonService
  ) {
    this.generateCalendar();
  }

  ngOnInit(): void {
    this.selectedDate = new Date(); // Select today by default
    this.loadShipments();
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  ionViewWillEnter() {
    this.currentDate = new Date();
    this.selectedDate = new Date();
    this.generateCalendar();
    this.loadShipments();
  }

  // Load shipments from API
  loadShipments(): void {
    console.log('Loading shipments for calendar...');
    this.loadingService.show();

    const apiPayload = {}; // Empty payload as per API specification
    this.subscription.add(
      this.dataService.getShipments(apiPayload).subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          console.log('Shipments loaded successfully for calendar');

          const data = response.data;
          if (Array.isArray(data)) {
            this.allShipments = this.transformApiDataToUIModel(data);
            this.organizeShipmentsByDate();
            this.updateSelectedDateShipments();
          } else {
            this.toastService.show('Failed to load shipments');
          }
        },
        error: (error) => {
          this.loadingService.hide();
          console.log('Error loading shipments for calendar:', error);
          this.toastService.show(error.message || 'An error occurred');
        }
      })
    );
  }

  // Transform API data to UI model
  private transformApiDataToUIModel(apiData: ShipmentData[]): DriverShipmentItem[] {
    return apiData.map(item => ({
      id: item.id,
      refId: item.refID,
      paymentType: this.formatPaymentType(item.paymentType),
      from: item.pickupAddressDetail?.city || 'N/A',
      to: item.deliveryAddressDetail?.city || 'N/A',
      etd: this.commonService.formatDate(item.etd),
      status: this.formatStatus(item.status),
      originalStatus: item.status,
      customerName: item.customerUserDetail?.fullName || 'N/A',
      grandTotal: item.grandTotal || 0,
      shipmentType: item.shipmentType,
      step: item.step,
      isCargoAdded: item.isCargoAdded || false
    }));
  }

  // Helper methods for formatting
  private formatPaymentType(paymentType: string): string {
    switch (paymentType?.toUpperCase()) {
      case 'PREPAID':
        return 'Prepaid';
      case 'COD':
        return 'COD';
      default:
        return paymentType || 'N/A';
    }
  }

  private formatStatus(status: string): string {
    switch (status?.toUpperCase()) {
      case 'ASSIGNED':
        return 'Assigned';
      case 'IN_TRANSIT':
        return 'In Transit';
      case 'COMPLETED':
        return 'Completed';
      case 'DELIVERED':
        return 'Delivered';
      case 'NEW':
        return 'New';
      case 'CANCELLED':
        return 'Cancelled';
      default:
        return status || 'N/A';
    }
  }

  // Organize shipments by date
  private organizeShipmentsByDate(): void {
    this.shipmentsByDate = {};

    this.allShipments.forEach(shipment => {
      if (shipment.etd) {
        // Parse the ETD date
        const etdDate = this.parseETDDate(shipment.etd);
        if (etdDate) {
          const dateKey = this.format(etdDate);

          if (!this.shipmentsByDate[dateKey]) {
            this.shipmentsByDate[dateKey] = [];
          }

          const calendarShipment: CalendarShipment = {
            id: shipment.id,
            refId: shipment.refId,
            title: this.getShipmentTitle(shipment),
            time: this.getShipmentTime(shipment),
            status: shipment.status,
            statusColor: this.getStatusColor(shipment.originalStatus || shipment.status),
            customerName: shipment.customerName,
            from: shipment.from,
            to: shipment.to,
            etd: shipment.etd
          };

          this.shipmentsByDate[dateKey].push(calendarShipment);
        }
      }
    });
  }

  // Parse ETD date from various formats
  private parseETDDate(etd: string): Date | null {
    try {
      // Handle different date formats that might come from the API
      if (etd.includes('-')) {
        // Format: "25-03-25" or "2025-03-25"
        const parts = etd.split(',')[0].trim(); // Remove time part if exists
        const dateParts = parts.split('-');

        if (dateParts.length === 3) {
          let year, month, day;

          if (dateParts[0].length === 4) {
            // Format: "2025-03-25"
            year = parseInt(dateParts[0]);
            month = parseInt(dateParts[1]) - 1; // Month is 0-indexed
            day = parseInt(dateParts[2]);
          } else {
            // Format: "25-03-25" (assuming YY-MM-DD)
            year = 2000 + parseInt(dateParts[0]);
            month = parseInt(dateParts[1]) - 1;
            day = parseInt(dateParts[2]);
          }

          return new Date(year, month, day);
        }
      }

      // Try parsing as ISO date
      const date = new Date(etd);
      return isNaN(date.getTime()) ? null : date;
    } catch (error) {
      console.warn('Failed to parse ETD date:', etd, error);
      return null;
    }
  }

  // Get shipment title based on status
  private getShipmentTitle(shipment: DriverShipmentItem): string {
    const status = shipment.originalStatus || shipment.status;
    switch (status.toUpperCase()) {
      case 'NEW':
      case 'ASSIGNED':
        return 'Shipment Pickup';
      case 'IN_TRANSIT':
        return 'Shipment In Transit';
      case 'COMPLETED':
      case 'DELIVERED':
        return 'Shipment Delivered';
      default:
        return 'Shipment';
    }
  }

  // Get shipment time display
  private getShipmentTime(shipment: DriverShipmentItem): string {
    return `${shipment.refId} • ${shipment.from} → ${shipment.to}`;
  }

  // Get status color for dots
  private getStatusColor(status: string): string {
    const statusOption = SHIPMENT_STATUS_OPTIONS.find(
      option => option.id.toUpperCase() === status.toUpperCase()
    );
    return statusOption ? statusOption.color : 'green';
  }

  // Update shipments for selected date
  private updateSelectedDateShipments(): void {
    if (this.selectedDate) {
      const dateKey = this.format(this.selectedDate);
      this.selectedDateShipments = this.shipmentsByDate[dateKey] || [];
    }
  }

  // Get icon color for shipment items
  getIconColor(statusColor: string): string {
    switch (statusColor) {
      case 'red':
        return '#f44336';
      case 'yellow':
        return '#ffc107';
      case 'green':
        return '#4caf50';
      default:
        return '#000000';
    }
  }

  generateCalendar() {
    const firstDay = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);
    const lastDay = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0);

    const weeks: Date[][] = [];
    let currentWeek: Date[] = [];

    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - startDate.getDay());

    const endDate = new Date(lastDay);
    endDate.setDate(endDate.getDate() + (6 - endDate.getDay()));

    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      currentWeek.push(new Date(d));
      if (currentWeek.length === 7) {
        weeks.push(currentWeek);
        currentWeek = [];
      }
    }

    this.weeks = weeks;
  }

  format(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  // Get status dots for a specific date
  getStatus(date: Date): string[] {
    const dateKey = this.format(date);
    const dayShipments = this.shipmentsByDate[dateKey] || [];

    if (dayShipments.length === 0) {
      return [];
    }

    // Get unique status colors for the day
    const statusColors = new Set<string>();
    dayShipments.forEach(shipment => {
      statusColors.add(shipment.statusColor);
    });

    return Array.from(statusColors);
  }

  isSameMonth(date: Date): boolean {
    return date.getMonth() === this.currentDate.getMonth();
  }

  isSelected(date: Date): boolean {
    return !!this.selectedDate && date.toDateString() === this.selectedDate?.toDateString();
  }

  selectDate(date: Date) {
    this.selectedDate = new Date(date);
    this.updateSelectedDateShipments();
  }

  goToPreviousMonth(): void {
    const newDate = new Date(this.currentDate);
    newDate.setMonth(this.currentDate.getMonth() - 1);
    this.currentDate = newDate;
    this.generateCalendar();
    this.organizeShipmentsByDate(); // Re-organize shipments for new month
    this.updateSelectedDateShipments();
  }

  goToNextMonth(): void {
    const newDate = new Date(this.currentDate);
    newDate.setMonth(this.currentDate.getMonth() + 1);
    this.currentDate = newDate;
    this.generateCalendar();
    this.organizeShipmentsByDate(); // Re-organize shipments for new month
    this.updateSelectedDateShipments();
  }

}
