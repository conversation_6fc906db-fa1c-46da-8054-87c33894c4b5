import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-driver-calendar',
  templateUrl: './driver-calendar.component.html',
  styleUrls: ['./driver-calendar.component.scss'],
  standalone: false
})
export class DriverCalendarComponent implements OnInit {

  currentDate: Date = new Date();
  weeks: Date[][] = [];
  selectedDate: Date | null = null;

  // Status dots for demo (green, yellow, red)
  staticEvents: { [key: string]: string[] } = {
    '2025-05-04': ['red'],
    '2025-05-08': ['green', 'yellow'],
    '2025-05-10': ['yellow'],
    '2025-05-13': ['red'],
    '2025-05-17': ['yellow'],
    '2025-05-18': ['green', 'yellow', 'red'],
    '2025-05-30': ['green', 'yellow'],
  };
  shipments = [
    {
      title: 'Shipment Pickup',
      time: 'Today, 11:00–15:00',
    },
    {
      title: 'Shipment Pickup',
      time: 'Today, 13:00–18:00',
    },
    {
      title: 'Shipment Delivered',
      time: 'Today, 15:30–20:00',
    },
  ];

  constructor() {
    this.generateCalendar();
  }

  ngOnInit(): void {

  }

  ionViewWillEnter() {
    this.currentDate = new Date();
    this.selectedDate = null;
    this.generateCalendar();
  }

  generateCalendar() {
    const firstDay = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);
    const lastDay = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0);

    const weeks: Date[][] = [];
    let currentWeek: Date[] = [];

    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - startDate.getDay());

    const endDate = new Date(lastDay);
    endDate.setDate(endDate.getDate() + (6 - endDate.getDay()));

    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      currentWeek.push(new Date(d));
      if (currentWeek.length === 7) {
        weeks.push(currentWeek);
        currentWeek = [];
      }
    }

    this.weeks = weeks;
  }

  format(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  getStatus(date: Date): string[] {
    return this.staticEvents[this.format(date)] || [];
  }

  isSameMonth(date: Date): boolean {
    return date.getMonth() === this.currentDate.getMonth();
  }

  isSelected(date: Date): boolean {
    return !!this.selectedDate && date.toDateString() === this.selectedDate?.toDateString();
  }

  selectDate(date: Date) {
    this.selectedDate = new Date(date);
  }

  goToPreviousMonth(): void {
    const newDate = new Date(this.currentDate);
    newDate.setMonth(this.currentDate.getMonth() - 1);
    this.currentDate = newDate;
    this.generateCalendar(); // 🔁 Update calendar
  }

  goToNextMonth(): void {
    const newDate = new Date(this.currentDate);
    newDate.setMonth(this.currentDate.getMonth() + 1);
    this.currentDate = newDate;
    this.generateCalendar(); // 🔁 Update calendar
  }

}
