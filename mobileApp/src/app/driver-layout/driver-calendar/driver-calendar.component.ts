import { Component, OnDestroy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { ToastService } from 'src/shared/toast.service';
import { RestResponse } from 'src/shared/auth.model';
import { ShipmentData, DriverShipmentItem } from 'src/modals/shipping-info';
import { CommonService } from 'src/services/common.service';

// Shipment status options with color mapping
const SHIPMENT_STATUS_OPTIONS = [
  { id: 'NEW', name: 'New', color: 'red' },
  { id: 'ASSIGNED', name: 'Assigned', color: 'red' },
  { id: 'IN_TRANSIT', name: 'In Transit', color: 'yellow' },
  { id: 'COMPLETED', name: 'Completed', color: 'green' },
  { id: 'DELIVERED', name: 'Delivered', color: 'green' },
  { id: 'CANCELLED', name: 'Cancelled', color: 'green' }
];

interface CalendarShipment {
  id: string;
  refId: string;
  title: string;
  time: string;
  status: string;
  statusColor: string;
  customerName: string;
  from: string;
  to: string;
  etd: string;
}

// Extended interface for calendar component
interface CalendarDriverShipmentItem extends DriverShipmentItem {
  formattedEtd?: string;
}

@Component({
  selector: 'app-driver-calendar',
  templateUrl: './driver-calendar.component.html',
  styleUrls: ['./driver-calendar.component.scss'],
  standalone: false
})
export class DriverCalendarComponent implements OnInit, OnDestroy {

  currentDate: Date = new Date();
  weeks: Date[][] = [];
  selectedDate: Date | null = null;

  // Shipment data
  allShipments: DriverShipmentItem[] = [];
  shipmentsByDate: { [key: string]: CalendarShipment[] } = {};
  selectedDateShipments: CalendarShipment[] = [];

  private subscription = new Subscription();

  constructor(
    private readonly dataService: DataService,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    private readonly commonService: CommonService
  ) {
    this.generateCalendar();
  }

  ngOnInit(): void {
    this.selectedDate = new Date(); // Select today by default
    this.loadShipments();
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  // Format date for API month filter (YYYY-MM format)
  private formatMonthForAPI(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    return `${year}-${month}`;
  }

  ionViewWillEnter() {
    console.log('📱 Calendar view will enter');
    this.currentDate = new Date();
    this.selectedDate = new Date();
    console.log('📅 Set current date:', this.currentDate);
    console.log('📅 Set selected date:', this.selectedDate);
    this.generateCalendar();
    this.loadShipments();
  }

  // Load shipments from calendar API
  loadShipments(): void {
    console.log('Loading shipments for calendar...');
    this.loadingService.show();

    // Create month filter for current displayed month
    const monthFilter = this.formatMonthForAPI(this.currentDate);
    const apiPayload = {
      filtering: {
        month: monthFilter
      }
    };

    console.log('📅 Calendar API Payload:', apiPayload);

    this.subscription.add(
      this.dataService.getCalendarShipments(apiPayload).subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();

          // 🚀 DETAILED API RESPONSE LOGGING
          console.log('='.repeat(80));
          console.log('🚀 SHIPMENTS API RESPONSE - COMPLETE STRUCTURE');
          console.log('='.repeat(80));
          console.log('📋 Response Object:', response);
          console.log('📊 Response Status:', response.status);
          console.log('📝 Response Message:', response.message);
          console.log('📦 Response Data Type:', typeof response.data);
          console.log('📦 Response Data Is Array:', Array.isArray(response.data));
          console.log('📦 Response Data Length:', response.data?.length);
          console.log('📦 Raw Data:', response.data);

          // Show first few shipments in detail
          if (Array.isArray(response.data) && response.data.length > 0) {
            console.log('🔍 FIRST 3 SHIPMENTS DETAILED:');
            response.data.slice(0, 3).forEach((item, index) => {
              console.log(`📦 Shipment ${index + 1}:`, {
                id: item.id,
                refID: item.refID,
                etd: item.etd,
                etdType: typeof item.etd,
                createdOn: item.createdOn,
                createdOnType: typeof item.createdOn,
                status: item.status,
                customerName: item.customerUserDetail?.fullName,
                pickupCity: item.pickupAddressDetail?.city,
                deliveryCity: item.deliveryAddressDetail?.city,
                fullObject: item
              });
            });
          }
          console.log('='.repeat(80));

          const data = response.data;
          if (Array.isArray(data)) {
            console.log(`📊 Found ${data.length} shipments from API`);
            this.allShipments = this.transformApiDataToUIModel(data);
            console.log('🔄 Transformed shipments:', this.allShipments);
            this.organizeShipmentsByDate();
            console.log('📅 Shipments organized by date:', this.shipmentsByDate);

            // Ensure we have a selected date, default to today if not set
            if (!this.selectedDate) {
              this.selectedDate = new Date();
              console.log('📅 Setting default selected date to today:', this.selectedDate);
            }

            this.updateSelectedDateShipments();
            console.log('📋 Selected date shipments:', this.selectedDateShipments);

            // Force update the UI after a short delay to ensure everything is rendered
            setTimeout(() => {
              console.log('🔄 Force updating selected date shipments after delay...');
              this.updateSelectedDateShipments();
            }, 100);
          } else {
            console.error('❌ API response data is not an array:', data);
            this.toastService.show('Failed to load shipments');
          }
        },
        error: (error: any) => {
          this.loadingService.hide();
          console.error('❌ Error loading shipments for calendar:', error);
          this.toastService.show(error.message || 'An error occurred');
        }
      })
    );
  }

  // Transform API data to UI model
  private transformApiDataToUIModel(apiData: ShipmentData[]): any[] {
    console.log('🔄 Starting transformation of API data...');
    console.log('📊 Raw API data sample:', apiData.slice(0, 2)); // Show first 2 items

    return apiData.map((item, index) => {
      console.log(`📦 Processing item ${index + 1}/${apiData.length}:`, {
        id: item.id,
        refID: item.refID,
        etd: item.etd,
        createdOn: item.createdOn,
        status: item.status,
        pickupCity: item.pickupAddressDetail?.city,
        deliveryCity: item.deliveryAddressDetail?.city,
        fullItem: item
      });

      const transformedItem = {
        id: item.id,
        refId: item.refID,
        paymentType: this.formatPaymentType(item.paymentType),
        from: item.pickupAddressDetail?.city || 'N/A',
        to: item.deliveryAddressDetail?.city || 'N/A',
        etd: item.etd || item.createdOn || '', // Use ETD if available, fallback to createdOn
        createdOn: item.createdOn, // Store createdOn separately
        formattedEtd: this.commonService.formatDate(item.etd || item.createdOn), // Store formatted version
        status: this.formatStatus(item.status),
        originalStatus: item.status,
        customerName: item.customerUserDetail?.fullName || 'N/A',
        grandTotal: item.grandTotal || 0,
        shipmentType: item.shipmentType || 'N/A',
        step: item.step,
        isCargoAdded: item.isCargoAdded || false
      };

      console.log(`✅ Transformed shipment ${item.refID}:`, transformedItem);

      return transformedItem;
    });
  }

  // Helper methods for formatting
  private formatPaymentType(paymentType: string): string {
    switch (paymentType?.toUpperCase()) {
      case 'PREPAID':
        return 'Prepaid';
      case 'COD':
        return 'COD';
      default:
        return paymentType || 'N/A';
    }
  }

  private formatStatus(status: string): string {
    switch (status?.toUpperCase()) {
      case 'ASSIGNED':
        return 'Assigned';
      case 'IN_TRANSIT':
        return 'In Transit';
      case 'COMPLETED':
        return 'Completed';
      case 'DELIVERED':
        return 'Delivered';
      case 'NEW':
        return 'New';
      case 'CANCELLED':
        return 'Cancelled';
      default:
        return status || 'N/A';
    }
  }

  // Organize shipments by date
  private organizeShipmentsByDate(): void {
    console.log('🗓️ Organizing shipments by date...');
    this.shipmentsByDate = {};

    this.allShipments.forEach((shipment, index) => {
      console.log(`📦 Processing shipment ${index + 1}:`, shipment);
      console.log(`📅 ETD value: "${shipment.etd}" (type: ${typeof shipment.etd})`);
      console.log(`📅 CreatedOn value: "${(shipment as any).createdOn}" (type: ${typeof (shipment as any).createdOn})`);

      // Use ETD if available, otherwise use createdOn
      const dateToUse = shipment.etd || (shipment as any).createdOn;
      console.log(`📅 Date to use for calendar: "${dateToUse}"`);

      if (dateToUse && dateToUse.trim() !== '') {
        console.log(`📅 Date found: "${dateToUse}"`);
        // Parse the date
        const parsedDate = this.parseETDDate(dateToUse);
        console.log(`🔍 Parsed date:`, parsedDate);

        if (parsedDate) {
          const dateKey = this.format(parsedDate);
          console.log(`🔑 Date key: "${dateKey}"`);

          if (!this.shipmentsByDate[dateKey]) {
            this.shipmentsByDate[dateKey] = [];
          }

          const calendarShipment: CalendarShipment = {
            id: shipment.id,
            refId: shipment.refId,
            title: this.getShipmentTitle(shipment),
            time: this.getShipmentTime(shipment),
            status: shipment.status,
            statusColor: this.getStatusColor(shipment.originalStatus || shipment.status),
            customerName: shipment.customerName,
            from: shipment.from,
            to: shipment.to,
            etd: dateToUse
          };

          this.shipmentsByDate[dateKey].push(calendarShipment);
          console.log(`✅ Added shipment to date ${dateKey}:`, calendarShipment);
        } else {
          console.warn(`⚠️ Could not parse date: "${dateToUse}"`);
        }
      } else {
        console.warn(`⚠️ No date found for shipment (both ETD and createdOn are null/empty):`, {
          shipment: shipment,
          etd: shipment.etd,
          createdOn: (shipment as any).createdOn
        });
      }
    });

    console.log('📊 Final shipmentsByDate:', this.shipmentsByDate);

    // Summary of dates with shipments
    const datesWithShipments = Object.keys(this.shipmentsByDate);
    console.log(`📈 Summary: ${datesWithShipments.length} dates have shipments`);
    datesWithShipments.forEach(dateKey => {
      const shipments = this.shipmentsByDate[dateKey];
      const statusCounts = shipments.reduce((acc, ship) => {
        acc[ship.statusColor] = (acc[ship.statusColor] || 0) + 1;
        return acc;
      }, {} as any);
      console.log(`📅 ${dateKey}: ${shipments.length} shipments`, statusCounts);
    });
  }

  // Parse ETD date from various formats
  private parseETDDate(etd: string): Date | null {
    console.log(`🔍 Parsing ETD: "${etd}" (length: ${etd.length})`);

    if (!etd || etd.trim() === '') {
      console.warn('⚠️ ETD is empty or null');
      return null;
    }

    try {
      // Clean the input
      const cleanEtd = etd.trim();
      console.log(`🧹 Cleaned ETD: "${cleanEtd}"`);

      // Try direct ISO parsing first (most common API format)
      console.log('🔄 Trying direct ISO date parsing...');
      let date = new Date(cleanEtd);
      if (!isNaN(date.getTime())) {
        console.log('✅ ISO parsing successful:', date);
        console.log('📅 Parsed date details:', {
          year: date.getFullYear(),
          month: date.getMonth() + 1,
          day: date.getDate(),
          dateString: date.toDateString(),
          isoString: date.toISOString()
        });
        return date;
      }

      // Handle dash-separated dates
      if (cleanEtd.includes('-')) {
        console.log('📅 Contains dash, parsing as dash-separated date');
        // Remove time part if exists (e.g., "2025-04-15T10:30:00" or "25-03-25, 11:30pm")
        const parts = cleanEtd.split(/[T,]/)[0].trim();
        console.log('📝 Date part after removing time:', parts);
        const dateParts = parts.split('-');
        console.log('🔢 Date parts:', dateParts);

        if (dateParts.length === 3) {
          let year, month, day;

          if (dateParts[0].length === 4) {
            // Format: "2025-03-25"
            console.log('📅 Format detected: YYYY-MM-DD');
            year = parseInt(dateParts[0]);
            month = parseInt(dateParts[1]) - 1; // Month is 0-indexed
            day = parseInt(dateParts[2]);
          } else if (dateParts[2].length === 4) {
            // Format: "25-03-2025" (DD-MM-YYYY)
            console.log('📅 Format detected: DD-MM-YYYY');
            day = parseInt(dateParts[0]);
            month = parseInt(dateParts[1]) - 1;
            year = parseInt(dateParts[2]);
          } else {
            // Format: "25-03-25" (assuming DD-MM-YY)
            console.log('📅 Format detected: DD-MM-YY');
            day = parseInt(dateParts[0]);
            month = parseInt(dateParts[1]) - 1;
            year = 2000 + parseInt(dateParts[2]);
          }

          console.log(`📊 Parsed values: Year=${year}, Month=${month}, Day=${day}`);
          const parsedDate = new Date(year, month, day);
          console.log('✅ Final parsed date:', parsedDate);
          return parsedDate;
        }
      }

      // Handle slash-separated dates (e.g., "04/15/2025")
      if (cleanEtd.includes('/')) {
        console.log('� Contains slash, parsing as slash-separated date');
        const parts = cleanEtd.split(/[T,]/)[0].trim();
        const dateParts = parts.split('/');
        console.log('🔢 Date parts:', dateParts);

        if (dateParts.length === 3) {
          // Assume MM/DD/YYYY format
          const month = parseInt(dateParts[0]) - 1;
          const day = parseInt(dateParts[1]);
          const year = parseInt(dateParts[2]);

          console.log(`📊 Parsed values: Year=${year}, Month=${month}, Day=${day}`);
          const parsedDate = new Date(year, month, day);
          console.log('✅ Final parsed date:', parsedDate);
          return parsedDate;
        }
      }

      console.error('❌ No matching date format found for:', cleanEtd);
      return null;
    } catch (error) {
      console.error('❌ Failed to parse ETD date:', etd, error);
      return null;
    }
  }

  // Get shipment title based on status
  private getShipmentTitle(shipment: DriverShipmentItem): string {
    const status = shipment.originalStatus || shipment.status;
    switch (status.toUpperCase()) {
      case 'NEW':
      case 'ASSIGNED':
        return 'Shipment Pickup';
      case 'IN_TRANSIT':
        return 'Shipment In Transit';
      case 'COMPLETED':
      case 'DELIVERED':
        return 'Shipment Delivered';
      default:
        return 'Shipment';
    }
  }

  // Get shipment time display
  private getShipmentTime(shipment: DriverShipmentItem): string {
    return `${shipment.refId} • ${shipment.from} → ${shipment.to}`;
  }

  // Get status color for dots
  private getStatusColor(status: string): string {
    const statusOption = SHIPMENT_STATUS_OPTIONS.find(
      option => option.id.toUpperCase() === status.toUpperCase()
    );
    return statusOption ? statusOption.color : 'green';
  }

  // Update shipments for selected date
  private updateSelectedDateShipments(): void {
    console.log('🔄 Updating selected date shipments...');

    if (this.selectedDate) {
      const dateKey = this.format(this.selectedDate);
      console.log(`📅 Selected date: ${this.selectedDate.toDateString()}`);
      console.log(`🔑 Looking for shipments with key: "${dateKey}"`);
      console.log('📊 Available date keys:', Object.keys(this.shipmentsByDate));
      console.log('📊 Total dates with shipments:', Object.keys(this.shipmentsByDate).length);

      this.selectedDateShipments = this.shipmentsByDate[dateKey] || [];
      console.log(`📋 Found ${this.selectedDateShipments.length} shipments for selected date:`, this.selectedDateShipments);

      // If no shipments found for today, show debug info
      if (this.selectedDateShipments.length === 0) {
        console.log('🔍 No shipments found for selected date. Checking nearby dates...');
        const nearbyDates = Object.keys(this.shipmentsByDate).slice(0, 5);
        nearbyDates.forEach(key => {
          console.log(`📅 Date ${key} has ${this.shipmentsByDate[key].length} shipments`);
        });
      }
    } else {
      console.log('⚠️ No date selected');
      this.selectedDateShipments = [];
    }
  }

  // Get icon color for shipment items
  getIconColor(statusColor: string): string {
    switch (statusColor) {
      case 'red':
        return '#f44336';
      case 'yellow':
        return '#ffc107';
      case 'green':
        return '#4caf50';
      default:
        return '#000000';
    }
  }

  generateCalendar() {
    const firstDay = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);
    const lastDay = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0);

    const weeks: Date[][] = [];
    let currentWeek: Date[] = [];

    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - startDate.getDay());

    const endDate = new Date(lastDay);
    endDate.setDate(endDate.getDate() + (6 - endDate.getDay()));

    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      currentWeek.push(new Date(d));
      if (currentWeek.length === 7) {
        weeks.push(currentWeek);
        currentWeek = [];
      }
    }

    this.weeks = weeks;
  }

  format(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  // Get status dots for a specific date
  getStatus(date: Date): string[] {
    const dateKey = this.format(date);
    const dayShipments = this.shipmentsByDate[dateKey] || [];

    if (dayShipments.length === 0) {
      return [];
    }

    // Get unique status colors for the day
    const statusColors = new Set<string>();
    dayShipments.forEach(shipment => {
      statusColors.add(shipment.statusColor);
    });

    const uniqueColors = Array.from(statusColors);

    // Debug logging for dates with shipments
    if (uniqueColors.length > 0) {
      console.log(`📅 Date ${dateKey} has ${dayShipments.length} shipments with colors:`, uniqueColors);
      console.log(`📦 Shipments:`, dayShipments.map(s => `${s.refId} (${s.statusColor})`));
    }

    return uniqueColors;
  }

  isSameMonth(date: Date): boolean {
    return date.getMonth() === this.currentDate.getMonth();
  }

  isSelected(date: Date): boolean {
    return !!this.selectedDate && date.toDateString() === this.selectedDate?.toDateString();
  }

  selectDate(date: Date) {
    console.log('📅 Date selected:', date.toDateString());
    this.selectedDate = new Date(date);
    this.updateSelectedDateShipments();
  }

  // Public method to refresh the schedule for current selected date
  refreshSchedule(): void {
    console.log('🔄 Manually refreshing schedule...');
    if (this.selectedDate) {
      this.updateSelectedDateShipments();
    } else {
      this.selectedDate = new Date();
      this.updateSelectedDateShipments();
    }
  }

  goToPreviousMonth(): void {
    const newDate = new Date(this.currentDate);
    newDate.setMonth(this.currentDate.getMonth() - 1);
    this.currentDate = newDate;
    this.generateCalendar();
    this.loadShipments(); // Reload shipments for new month
  }

  goToNextMonth(): void {
    const newDate = new Date(this.currentDate);
    newDate.setMonth(this.currentDate.getMonth() + 1);
    this.currentDate = newDate;
    this.generateCalendar();
    this.loadShipments(); // Reload shipments for new month
  }

  // 🔍 DEBUG METHOD - Show API Response in Alert
  showApiResponse(): void {
    console.log('🔍 Manually triggering calendar API call to show response...');
    this.loadingService.show();

    const monthFilter = this.formatMonthForAPI(this.currentDate);
    const apiPayload = {
      filtering: {
        month: monthFilter
      }
    };

    console.log('🔍 Debug API Payload:', apiPayload);
    this.dataService.getCalendarShipments(apiPayload).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();

        // Create a summary for alert
        const summary = {
          status: response.status,
          message: response.message,
          dataType: typeof response.data,
          isArray: Array.isArray(response.data),
          count: response.data?.length || 0,
          firstShipment: response.data?.[0] || null
        };

        // Show in alert
        alert(`API Response Summary:\n\nStatus: ${summary.status}\nMessage: ${summary.message}\nData Type: ${summary.dataType}\nIs Array: ${summary.isArray}\nCount: ${summary.count}\n\nFirst Shipment ETD: ${summary.firstShipment?.etd}\nFirst Shipment Status: ${summary.firstShipment?.status}\n\nCheck console for full details!`);

        // Also log to console
        console.log('🔍 API Response Summary:', summary);
        console.log('🔍 Full API Response:', response);
      },
      error: (error) => {
        this.loadingService.hide();
        alert(`API Error: ${error.message || 'Unknown error'}`);
        console.error('🔍 API Error:', error);
      }
    });
  }

}
