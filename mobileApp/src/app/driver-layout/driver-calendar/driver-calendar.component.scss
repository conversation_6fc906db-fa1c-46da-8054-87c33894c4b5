 .calendar-tab-page {
     --background: white;
     height: 100%;

     .calendar-tab-body-section {
         display: inline-block;
         width: 100%;
         height: calc(100vh - 210px);
         padding: 0px 20px 10px 20px !important;
         overflow-y: auto;

         .calendar-header {
             display: flex;
             justify-content: space-between;
             align-items: center;
             //   padding: 1rem;
             font-weight: bold;
             font-size: 1.2rem;
             padding: 0px 5px;
             //   background: #ffeb3b;
             //   border-bottom: 1px solid #ddd;

             .calendar-icon-header-container {
                 display: flex;
                 justify-content: space-between;
                 align-items: center;
                 gap: 10px;
             }

             .calendar-icon-header {
                 display: grid;
                 padding: 6px;
                 background: #EFEFEF;
                 border-radius: 23px;
             }
         }

         .calendar-grid {
             margin-top: 20px;
             display: grid;
             grid-template-columns: repeat(7, 1fr);
             gap: 0.5rem;
             padding: 1rem;
             background: white;
             border-radius: 23px;
             box-shadow: 0 5px 7px rgba(0, 0, 0, 0.1);

             .day-header {
                 text-align: center;
                 font-weight: bold;
                 color: #666;
             }

             .day-cell {
                 display: contents;
             }

             .date-box {
                 text-align: center;
                 padding: 0.6rem 0.3rem;
                 border-radius: 50%;
                 position: relative;
                 cursor: pointer;
             }

             .date-box.inactive {
                 opacity: 0.3;
             }

             .date-box.selected {
                 background-color: #ffd700;
                 color: #000;
             }

             .status-dots {
                 margin-top: 0.2rem;
                 display: flex;
                 justify-content: center;
                 gap: 2px;
             }

             .green-dot,
             .yellow-dot,
             .red-dot {
                 width: 6px;
                 height: 6px;
                 border-radius: 50%;
             }

             .green-dot {
                 background-color: #4caf50;
             }

             .yellow-dot {
                 background-color: #ffc107;
             }

             .red-dot {
                 background-color: #f44336;
             }
         }

         .shipment-schedule-container {
             display: flex;
             flex-direction: column;

             .schedule-text {
                 font-size: 16px;
                 font-weight: bold;
             }

             .shipment-list {
                 display: flex;
                 flex-direction: column;
                 gap: 15px;

                 .shipment-item {
                     display: flex;
                     align-items: center;
                     background-color: #ffeb3b;
                     padding: 12px;
                     border-radius: 14px;
                     box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
                 }

                 .icon-circle {
                     width: 15px;
                     height: 15px;
                     background-color: black;
                     border-radius: 50%;
                     margin-right: 10px;
                     display: flex;
                     align-items: center;
                     justify-content: center;

                     ion-icon {
                         color: #fff;
                         font-size: 10px;
                     }
                 }

                 .shipment-details {
                     display: flex;
                     flex-direction: column;
                     gap: 3px;

                     .shipment-title {
                         font-weight: bold;
                         font-size: 14px;
                         color: #000;
                     }

                     .shipment-time {
                         font-size: 12px;
                         color: #555;
                     }

                     .shipment-status {
                         font-size: 11px;
                         color: #777;
                         font-style: italic;
                     }
                 }
             }

             .no-shipments-message {
                 text-align: center;
                 padding: 20px;
                 color: #888;
                 font-style: italic;

                 p {
                     margin: 0;
                     font-size: 14px;
                 }
             }
         }
     }
 }