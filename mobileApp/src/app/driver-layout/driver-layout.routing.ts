import { Routes } from "@angular/router";
import { DriverLayoutComponent } from "./driver-layout.component";
import { HomeComponent } from "./home/<USER>";
import { DriverShippingComponent } from "./driver-shipping/driver-shipping.component";
import { DriverFuelReceiptComponent } from "./driver-fuel-receipt/driver-fuel-receipt.component";
import { DriverCalendarComponent } from "./driver-calendar/driver-calendar.component";
import { DriverAccountComponent } from "./driver-account/driver-account.component";
import { BasicInfoComponent } from "./driver-shipping/add-shipping-details/basic-info/basic-info.component";
import { OtherInfoComponent } from "./driver-shipping/add-shipping-details/other-info/other-info.component";
import { PickupDeliveryComponent } from "./driver-shipping/add-shipping-details/pickup-delivery/pickup-delivery.component";
import { DeliveryLocationComponent } from "./driver-shipping/add-shipping-details/delivery-location/delivery-location.component";
import { ShipmentCargoListingComponent } from "./driver-shipping/add-shipping-details/shipment-cargo-listing/shipment-cargo-listing.component";
import { AddCargoDetailsComponent } from "./driver-shipping/add-shipping-details/add-cargo-details/add-cargo-details.component";
import { SpecialRequestComponent } from "./driver-shipping/add-shipping-details/special-request/special-request.component";
import { AddDocumentsComponent } from "./driver-shipping/add-shipping-details/add-documents/add-documents.component";
import { StartShipmentComponent } from "./start-shipment/start-shipment.component";
import { PodManagementComponent } from "./pod-management/pod-management.component";

export const DRIVERLAYOUTROUTING: Routes = [
    {
        path: "",
        component: DriverLayoutComponent,
        children: [
            {
                path: "dashboard",
                component: HomeComponent
            },
            {
                path: "shipping",
                component: DriverShippingComponent
            },
            {
                path: "fuel-receipt",
                component: DriverFuelReceiptComponent
            },
            {
                path: "calendar",
                component: DriverCalendarComponent
            },
            {
                path: "account",
                component: DriverAccountComponent
            },
            {
                path: 'basic/info',
                component: BasicInfoComponent,
            },
            {
                path: 'other/info',
                component: OtherInfoComponent,
            },
            {
                path: 'pickup/delivery',
                component: PickupDeliveryComponent,
            },
            {
                path: 'delivery/location',
                component: DeliveryLocationComponent,
            },
            {
                path: 'cargo/listing',
                component: ShipmentCargoListingComponent,
            },
            {
                path: 'add/cargo/details',
                component: AddCargoDetailsComponent,
            },
            {
                path: 'special/request',
                component: SpecialRequestComponent,
            },
            {
                path: 'add/documents',
                component: AddDocumentsComponent,
            },
            {
                path: 'start-shipment',
                component: StartShipmentComponent,
            },
            {
                path: 'pod-management',
                component: PodManagementComponent,
            },
        ],
    },
];
