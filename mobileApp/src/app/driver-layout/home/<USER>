 .home-page {
     --background: #FCFCFC;
     height: 100%;

     .dashboard-header-section {
         position: relative;

         .dashboard-header-container {
             display: flex;
             align-items: center;
             padding: 20px 15px;
             background: #FFEA00;
             flex-direction: column;
             border-bottom-left-radius: 35px;
             border-bottom-right-radius: 35px;
         }

         .dashboard-icon-container {
             display: flex;
             align-items: center;
             justify-content: space-between;
             width: 100%;

             .dashboard-icon-background {
                 background-color: #fff;
                 width: 45px;
                 height: 45px;
                 border-radius: 50%;
                 display: flex;
                 align-items: center;
                 justify-content: center;
                 cursor: pointer;

                 img {
                     width: 100%;
                     height: 100%;
                     object-fit: cover;
                     border-radius: 50%;
                 }
             }

             .dashboard-user-info {
                 flex: 1;
                 margin: 0 12px;
                 display: flex;
                 flex-direction: column;
                 justify-content: center;

                 .username {
                     font-weight: 400;
                     font-size: 15px;
                     color: #000;

                     .name {
                         text-transform: capitalize;
                     }
                 }

                 .location {
                     font-size: 12px;
                     color: #888;
                 }
             }
         }

         .tracking-container {
             display: flex;
             align-items: center;
             justify-content: center;

             .tracking-icon {
                 font-size: 24px;
                 border-radius: 65px;
                 padding: 10px;
                 background: black;
             }
         }
     }

     .home-page-body-section {
         height: calc(100vh - 244px);
         display: block;
         width: 100%;
         padding: 20px 15px 10px 15px !important;
         overflow-y: auto;

         .logistics-text {
             font-size: 17px;
             font-weight: bold;
             margin-bottom: 20px;
         }

         .logistics-management-container {
             display: grid;
             grid-template-columns: 1fr 1fr;
             grid-template-rows: 1fr 1fr;
             gap: 15px;
             width: 100%;
             margin-top: 20px;

             .total-shipment-container {
                 display: flex;
                 flex-direction: column;
                 background: white;
                 box-shadow: 0 5px 7px rgba(0, 0, 0, 0.1);
                 border-radius: 25px;
                 padding: 25px 15px;
                 align-items: center;
                 justify-content: center;
                 gap: 8px;
                 min-height: 140px;
                 width: 100%;
                 box-sizing: border-box;
                 position: relative;

                 // All cards have consistent styling
                 &.upcoming-shipments,
                 &.progress-shipments,
                 &.complete-shipments {
                     padding: 25px 15px;
                     margin: 0;
                 }
             }

             .shipment-icon {
                 font-size: 50px;
                 margin-bottom: 8px;
             }

             .shipment-text {
                 font-size: 11px;
                 font-weight: 600;
                 text-align: center;
                 line-height: 1.2;
                 margin: 0 5px;
                 color: #333;
             }

             .shipment-count {
                 font-size: 24px;
                 font-weight: bold;
                 margin-top: 5px;
                 color: #000;
             }
         }

         // Responsive design for different screen sizes
         @media (max-width: 480px) {
             padding: 15px 10px 10px 10px !important;

             .logistics-management-container {
                 gap: 12px;
                 margin-top: 15px;

                 .total-shipment-container {
                     padding: 20px 12px;
                     min-height: 130px;
                 }

                 .shipment-icon {
                     font-size: 45px;
                 }

                 .shipment-text {
                     font-size: 10px;
                 }

                 .shipment-count {
                     font-size: 22px;
                 }
             }
         }

         @media (min-width: 768px) {
             .logistics-management-container {
                 gap: 20px;
                 margin-top: 25px;

                 .total-shipment-container {
                     padding: 30px 20px;
                     min-height: 160px;
                 }

                 .shipment-icon {
                     font-size: 55px;
                 }

                 .shipment-text {
                     font-size: 12px;
                 }

                 .shipment-count {
                     font-size: 26px;
                 }
             }
         }
     }
 }