import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, catchError } from 'rxjs';
import { environment } from 'src/environments/environment';
import { RestResponse } from 'src/shared/auth.model';

@Injectable({
  providedIn: 'root'
})
export class DataService {

  constructor(private readonly http: HttpClient) {
  }

  getRecords(path: string): Observable<any> {
    return this.http
      .get(environment.BaseApiUrl + path, { headers: environment.AppHeaders })
      .pipe(catchError(this.handleError));
  }

  getRecordsById(path: string, payload: any): Observable<any> {
    return this.http
      .get(environment.BaseApiUrl + path + payload, { headers: environment.AppHeaders })
      .pipe(catchError(this.handleError));
  }

  saveRecord(path: string, resource: any): Observable<any> {
    return this.http
      .post(environment.BaseApiUrl + path, resource, { headers: environment.AppHeaders })
      .pipe(catchError(this.handleError));
  }

  updateRecord(path: string, resource: any): Observable<any> {
    return this.http
      .put(environment.BaseApiUrl + path, resource, { headers: environment.AppHeaders })
      .pipe(catchError(this.handleError));
  }

  removeRecord(path: string): Observable<any> {
    return this.http
      .delete(environment.BaseApiUrl + path, { headers: environment.AppHeaders })
      .pipe(catchError(this.handleError));
  }

  postUrl(path: string): Observable<RestResponse> {
    return this.http
      .post<RestResponse>(environment.BaseApiUrl + path, null, { headers: environment.AppHeaders })
      .pipe(
        catchError((error) => this.handleError(error))
      );
  }

  protected handleError(error: HttpErrorResponse): Promise<any> {
    if (error.status === 404) {
      return Promise.reject({ "message": error.message ? error.message : 'Something went wrong while processing your request' });
    }
    const internalError: any = JSON.parse(JSON.stringify(error));
    if (!internalError.message) {
      internalError.message = 'Something went wrong while processing your request';
    }
    return Promise.reject(internalError);
  }

  customerOnboarding(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/customer/app/register', data);
  }

  login(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/customer/app/login', data);
  }

  forgotPassword(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/customer/app/forgot/password', data);
  }

  otpVerify(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/customer/app/forgot/verfication', data);
  }

  resendOtp(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/customer/app/forgot/otp/resend', data);
  }

  resetPassword(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/customer/app/reset/password', data);
  }

  uploadFile(data: FormData): Observable<RestResponse> {
    return this.http.post<RestResponse>(`${environment.BaseApiUrl}/api/file/group/items/upload`, data);
  }

  getShipments(data: any = {}): Observable<RestResponse> {
    return this.saveRecord('/api/app/driver/shipments', data);
  }

  getCalendarShipments(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/driver/shipments/calender', data);
  }

  getRefId(): Observable<RestResponse> {
    return this.getRecords('/api/shipment/refId');
  }

  getAllCustomers(): Observable<RestResponse> {
    return this.saveRecord('/api/account/app/customer/shipment/selection', {});
  }

  saveShipment(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/app/driver/shipment', data);
  }

  getCargoList(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/app/driver/ShipmentItems', data);
  }

  saveCargoItem(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/app/driver/shipmentitem', data);
  }

  saveShipmentSpecials(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/app/driver/shipment/specials', data);
  }

  startShipment(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/app/driver/shipment/start', data);
  }

  submitPOD(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/app/driver/shipment/pod', data);
  }

  logout(input: any): Observable<RestResponse> {
    return this.saveRecord(`/api/account/logout`, input);
  }

  // User Profile APIs
  getUserProfile(): Observable<RestResponse> {
    return this.getRecords('/api/customer/app/userprofile');
  }

  updateUserProfile(data: any): Observable<RestResponse> {
    return this.updateRecord('/api/customer/app/userprofile', data);
  }

}


