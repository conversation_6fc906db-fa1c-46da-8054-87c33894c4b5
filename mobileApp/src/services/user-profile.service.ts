import { Injectable } from '@angular/core';
import { Observable, tap } from 'rxjs';
import { DataService } from './data.service';
import { UserStateService } from './user-state.service';
import { UserProfileResponse, UserProfileModel } from 'src/modals/user-profile-response';
import { RestResponse } from 'src/shared/auth.model';

@Injectable({
  providedIn: 'root'
})
export class UserProfileService {

  constructor(
    private dataService: DataService,
    private userStateService: UserStateService
  ) { }

  /**
   * Get user profile from API and update global state
   * @returns Observable<UserProfileResponse>
   */
  getUserProfile(): Observable<UserProfileResponse> {
    return this.dataService.getUserProfile().pipe(
      tap((response: UserProfileResponse) => {
        if (response.status === 200 && response.data) {
          // Update global state with fresh profile data
          this.userStateService.updateUserProfile(response.data);
        }
      })
    );
  }

  /**
   * Update user profile and refresh global state
   * @param profileData UserProfileModel
   * @returns Observable<RestResponse>
   */
  updateUserProfile(profileData: UserProfileModel): Observable<RestResponse> {
    const updateData = profileData.toApiFormat();
    return this.dataService.updateUserProfile(updateData).pipe(
      tap((response: RestResponse) => {
        if (response.status === 200) {
          // After successful update, fetch fresh data to update global state
          this.getUserProfile().subscribe();
        }
      })
    );
  }

  /**
   * Validate profile data
   * @param profileData UserProfileModel
   * @param form Form reference
   * @returns boolean
   */
  validateProfile(profileData: UserProfileModel, form: any): boolean {
    return profileData.isValid(form);
  }

  /**
   * Force refresh user profile from API
   * @returns Observable<UserProfileResponse>
   */
  refreshUserProfile(): Observable<UserProfileResponse> {
    return this.getUserProfile();
  }

  /**
   * Get current user display name from global state
   * @returns string
   */
  getCurrentDisplayName(): string {
    return this.userStateService.getCurrentDisplayName();
  }

  /**
   * Get observable for user display name changes
   * @returns Observable<string>
   */
  getUserDisplayName$(): Observable<string> {
    return this.userStateService.userDisplayName$;
  }
}
