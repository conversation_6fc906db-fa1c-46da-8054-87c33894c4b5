import { Injectable } from '@angular/core';
import { StatusBar, Style } from '@capacitor/status-bar';
import { Platform } from '@ionic/angular';

export interface StatusBarConfig {
  backgroundColor: string;
  style: Style;
}

@Injectable({
  providedIn: 'root'
})
export class StatusBarService {

  // Define color schemes for different sections
  private readonly colorSchemes = {
    authentication: {
      backgroundColor: '#FFEA00', // Yellow
      style: Style.Dark
    },
    driverPortal: {
      backgroundColor: '#FFEA00', // Yellow
      style: Style.Dark
    },
    clientPortal: {
      backgroundColor: '#FFEA00', // Yellow - can be different
      style: Style.Dark
    },
    dashboard: {
      backgroundColor: '#FFEA00', // Yellow
      style: Style.Dark
    },
    shipping: {
      backgroundColor: '#FFEA00', // Blue - example for shipping pages
      style: Style.Light
    },
    account: {
      backgroundColor: '#FFEA00', // Green - example for account pages
      style: Style.Light
    },
    default: {
      backgroundColor: '#FFEA00', // Default yellow
      style: Style.Dark
    }
  };

  constructor(private platform: Platform) { }

  /**
   * Initialize status bar with default settings
   */
  async initialize(): Promise<void> {
    if (!this.platform.is('capacitor')) {
      return; // Only run on native platforms
    }

    try {
      await StatusBar.setOverlaysWebView({ overlay: false });
      await this.setColorScheme('default');
    } catch (error) {
    }
  }

  /**
   * Set status bar color scheme by name
   */
  async setColorScheme(schemeName: keyof typeof this.colorSchemes): Promise<void> {
    if (!this.platform.is('capacitor')) {
      return;
    }

    const scheme = this.colorSchemes[schemeName] || this.colorSchemes.default;
    await this.setStatusBar(scheme);
  }

  /**
   * Set custom status bar configuration
   */
  async setCustomStatusBar(config: StatusBarConfig): Promise<void> {
    if (!this.platform.is('capacitor')) {
      return;
    }

    await this.setStatusBar(config);
  }

  /**
   * Set status bar based on route
   */
  async setStatusBarForRoute(url: string): Promise<void> {
    if (!this.platform.is('capacitor')) {
      return;
    }

    let schemeName: keyof typeof this.colorSchemes = 'default';

    // Route-based color scheme selection
    if (url.startsWith('/account/')) {
      schemeName = 'authentication';
    } else if (url.startsWith('/portal/dashboard')) {
      schemeName = 'dashboard';
    } else if (url.startsWith('/portal/shipping')) {
      schemeName = 'shipping';
    } else if (url.startsWith('/portal/account')) {
      schemeName = 'account';
    } else if (url.startsWith('/portal/')) {
      schemeName = 'driverPortal';
    } else if (url.startsWith('/client-portal/')) {
      schemeName = 'clientPortal';
    }

    await this.setColorScheme(schemeName);
  }

  /**
   * Update color scheme configuration
   */
  updateColorScheme(schemeName: keyof typeof this.colorSchemes, config: StatusBarConfig): void {
    this.colorSchemes[schemeName] = config;
  }

  /**
   * Get current color scheme
   */
  getColorScheme(schemeName: keyof typeof this.colorSchemes): StatusBarConfig {
    return this.colorSchemes[schemeName] || this.colorSchemes.default;
  }

  /**
   * Private method to actually set the status bar
   */
  private async setStatusBar(config: StatusBarConfig): Promise<void> {
    try {
      await StatusBar.setBackgroundColor({ color: config.backgroundColor });
      await StatusBar.setStyle({ style: config.style });
    } catch (error) {
    }
  }

  /**
   * Set status bar to transparent (for full-screen content)
   */
  async setTransparent(): Promise<void> {
    if (!this.platform.is('capacitor')) {
      return;
    }

    try {
      await StatusBar.setOverlaysWebView({ overlay: true });
      await StatusBar.setBackgroundColor({ color: '#00000000' }); // Transparent
    } catch (error) {
    }
  }

  /**
   * Reset to default non-overlay mode
   */
  async resetOverlay(): Promise<void> {
    if (!this.platform.is('capacitor')) {
      return;
    }

    try {
      await StatusBar.setOverlaysWebView({ overlay: false });
    } catch (error) {
    }
  }
}
