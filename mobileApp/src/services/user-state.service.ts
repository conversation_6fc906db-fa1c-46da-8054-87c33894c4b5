import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { UserProfileData } from 'src/modals/user-profile-response';

@Injectable({
  providedIn: 'root'
})
export class UserStateService {

  // BehaviorSubject to hold current user profile data
  private userProfileSubject = new BehaviorSubject<UserProfileData | null>(null);
  
  // Observable for components to subscribe to
  public userProfile$ = this.userProfileSubject.asObservable();

  // BehaviorSubject for user display name (firstName + lastName)
  private userDisplayNameSubject = new BehaviorSubject<string>('');
  public userDisplayName$ = this.userDisplayNameSubject.asObservable();

  constructor() { }

  /**
   * Update the user profile data and notify all subscribers
   * @param profileData UserProfileData
   */
  updateUserProfile(profileData: UserProfileData): void {
    this.userProfileSubject.next(profileData);
    this.updateDisplayName(profileData);
  }

  /**
   * Get current user profile data (synchronous)
   * @returns UserProfileData | null
   */
  getCurrentUserProfile(): UserProfileData | null {
    return this.userProfileSubject.value;
  }

  /**
   * Get current user display name (synchronous)
   * @returns string
   */
  getCurrentDisplayName(): string {
    return this.userDisplayNameSubject.value;
  }

  /**
   * Update display name based on profile data
   * @param profileData UserProfileData
   */
  private updateDisplayName(profileData: UserProfileData): void {
    if (profileData && profileData.firstName && profileData.lastName) {
      const displayName = `${profileData.firstName} ${profileData.lastName}`;
      this.userDisplayNameSubject.next(displayName);
    } else if (profileData && profileData.firstName) {
      this.userDisplayNameSubject.next(profileData.firstName);
    } else {
      this.userDisplayNameSubject.next('User');
    }
  }

  /**
   * Clear user profile data (for logout)
   */
  clearUserProfile(): void {
    this.userProfileSubject.next(null);
    this.userDisplayNameSubject.next('');
  }

  /**
   * Get user's first name
   * @returns string
   */
  getFirstName(): string {
    const profile = this.getCurrentUserProfile();
    return profile?.firstName || '';
  }

  /**
   * Get user's last name
   * @returns string
   */
  getLastName(): string {
    const profile = this.getCurrentUserProfile();
    return profile?.lastName || '';
  }

  /**
   * Get user's email
   * @returns string
   */
  getEmail(): string {
    const profile = this.getCurrentUserProfile();
    return profile?.email || '';
  }

  /**
   * Get user's phone number
   * @returns string
   */
  getPhoneNumber(): string {
    const profile = this.getCurrentUserProfile();
    return profile?.phoneNumber || '';
  }

  /**
   * Check if user profile is loaded
   * @returns boolean
   */
  isProfileLoaded(): boolean {
    return this.userProfileSubject.value !== null;
  }
}
