/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;900&&family=Oswald:wght@200..700&family=Pacifico&display=swap");
@import "theme/variables.scss";
@import "theme/common.scss";
@import "theme/driver-dashboard.scss";
@import "theme/driver-register.scss";

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
/* @import "@ionic/angular/css/palettes/dark.class.css"; */

.loading-container {
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    position: fixed;
    z-index: 999999999;
    top: 0;

    .loader {
        margin: 0px auto;
    }

    svg {
        border-radius: 50%;
    }
}


/**
 * Mobile App Specific Styles
 * -----------------------------------------------------
 * Ensures proper mobile behavior and keyboard handling
 */

// Ensure proper mobile viewport
html,
body {
    height: 100vh;
    width: 100%;
    -webkit-overflow-scrolling: touch;
}

// Ensure ion-app takes full height
ion-app {
    height: 100vh;
}

// Fix for iOS keyboard issues
ion-content {
    --keyboard-offset: 0px;
}

// Prevent zoom on input focus (iOS)
input,
textarea,
select {
    font-size: 16px !important;
    -webkit-user-select: text;
    user-select: text;
}

// Smooth scrolling for mobile
* {
    -webkit-overflow-scrolling: touch;
}

// Fix for safe area on newer devices
.safe-area-top {
    padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
}

// Ensure proper touch behavior
.ion-page {
    touch-action: manipulation;
}

// Fix for keyboard overlay issues
ion-modal,
ion-popover {
    --backdrop-opacity: 0.4;
}

/**
 * Enhanced Ripple Effects
 * -----------------------------------------------------
 * Improved ripple effects for better user interaction
 */

// Enhanced ripple effect for better visibility
ion-ripple-effect {
    --ripple-color: currentColor;
    opacity: 0.2;
    transition: opacity 0.3s ease;
}

// Interactive elements with ripple effects
.interactive-element,
.add-shipping-container,
.shipping-tab-item,
.action-button,
.action-icons ion-icon {
    position: relative;
    overflow: hidden;
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;

    // Ensure ripple effect is visible
    ion-ripple-effect {
        z-index: 1;
        pointer-events: none;
    }
}

// Enhanced touch feedback
.interactive-element:active,
.add-shipping-container:active,
.shipping-tab-item:active,
.action-button:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
}

// Smooth transitions for all interactive elements
.interactive-element,
.add-shipping-container,
.shipping-tab-item,
.action-button,
.action-icons ion-icon {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

// Custom ripple colors for different elements
.primary-ripple ion-ripple-effect {
    --ripple-color: rgba(255, 234, 0, 0.4);
}

.secondary-ripple ion-ripple-effect {
    --ripple-color: rgba(255, 255, 255, 0.3);
}

.dark-ripple ion-ripple-effect {
    --ripple-color: rgba(0, 0, 0, 0.2);
}

/**
 * Custom Logout Action Sheet Styles
 * -----------------------------------------------------
 * Styled to match the app's design system with yellow accent
 * Slides up from bottom like the uploaded design
 */

.custom-logout-action-sheet {
    --backdrop-opacity: 0.4;

    .action-sheet-wrapper {
        border-radius: 20px 20px 0 0;
        background: white;
        box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.2);
        margin: 0;
        padding: 0;
    }

    .action-sheet-container {
        padding: 40px 30px 30px;
        text-align: center;
        position: relative;

        // Add yellow icon before header
        &::before {
            content: '';
            display: block;
            width: 80px;
            height: 80px;
            background: var(--ion-color-site-main-heading, #f6be00);
            border-radius: 50%;
            margin: 0 auto 20px;
            position: relative;
            box-shadow: 0 4px 15px rgba(246, 190, 0, 0.3);
        }

        // Add logout icon
        &::after {
            content: '🚪';
            position: absolute;
            top: 60px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 30px;
            color: white;
            z-index: 1;
        }
    }

    .action-sheet-title {
        font-size: 22px !important;
        font-weight: 600 !important;
        color: var(--ion-color-dark, #222428) !important;
        margin: 20px 0 12px 0 !important;
        font-family: var(--ion-font-family, 'Montserrat', sans-serif) !important;
        text-align: center !important;
    }

    .action-sheet-sub-title {
        font-size: 16px !important;
        color: var(--ion-color-medium, #92949c) !important;
        margin: 0 0 30px 0 !important;
        line-height: 1.4 !important;
        font-family: var(--ion-font-family, 'Montserrat', sans-serif) !important;
        text-align: center !important;
    }

    .action-sheet-button-container {
        display: flex;
        gap: 12px;
        padding: 0 30px 30px;
        flex-direction: row;
    }

    .action-sheet-button {
        padding: 16px 24px !important;
        border-radius: 12px !important;
        font-size: 16px !important;
        font-weight: 600 !important;
        border: none !important;
        margin: 0 !important;
        flex: 1;
        transition: all 0.2s ease !important;
        font-family: var(--ion-font-family, 'Montserrat', sans-serif) !important;

        &:active {
            transform: scale(0.98);
        }
    }

    .logout-button {
        background: var(--ion-color-dark, #222428) !important;
        color: white !important;

        &:hover {
            background: var(--ion-color-dark-shade, #1e2023) !important;
            box-shadow: 0 4px 12px rgba(34, 36, 40, 0.3) !important;
        }
    }

    .cancel-button {
        background: var(--ion-color-light, #f4f5f8) !important;
        color: var(--ion-color-dark, #222428) !important;
        border: 2px solid var(--ion-color-step-200, #cccccc) !important;

        &:hover {
            background: var(--ion-color-step-100, #e6e6e6) !important;
            border-color: var(--ion-color-step-300, #b3b3b3) !important;
        }
    }
}

// Responsive design for action sheet
@media (max-width: 480px) {
    .custom-logout-action-sheet {
        .action-sheet-container {
            padding: 30px 20px 20px;

            &::before {
                width: 70px;
                height: 70px;
            }

            &::after {
                top: 50px;
                font-size: 25px;
            }
        }

        .action-sheet-title {
            font-size: 20px !important;
        }

        .action-sheet-sub-title {
            font-size: 15px !important;
        }

        .action-sheet-button-container {
            padding: 0 20px 20px;
        }
    }
}

