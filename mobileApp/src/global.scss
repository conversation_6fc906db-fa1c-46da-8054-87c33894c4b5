/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;900&&family=Oswald:wght@200..700&family=Pacifico&display=swap");
@import "theme/variables.scss";
@import "theme/common.scss";
@import "theme/driver-dashboard.scss";
@import "theme/driver-register.scss";

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
/* @import "@ionic/angular/css/palettes/dark.class.css"; */

.loading-container {
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    position: fixed;
    z-index: 999999999;
    top: 0;

    .loader {
        margin: 0px auto;
    }

    svg {
        border-radius: 50%;
    }
}


/**
 * Mobile App Specific Styles
 * -----------------------------------------------------
 * Ensures proper mobile behavior and keyboard handling
 */

// Ensure proper mobile viewport
html,
body {
    height: 100vh;
    width: 100%;
    -webkit-overflow-scrolling: touch;
}

// Ensure ion-app takes full height
ion-app {
    height: 100vh;
}

// Fix for iOS keyboard issues
ion-content {
    --keyboard-offset: 0px;
}

// Prevent zoom on input focus (iOS)
input,
textarea,
select {
    font-size: 16px !important;
    -webkit-user-select: text;
    user-select: text;
}

// Smooth scrolling for mobile
* {
    -webkit-overflow-scrolling: touch;
}

// Fix for safe area on newer devices
.safe-area-top {
    padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
}

// Ensure proper touch behavior
.ion-page {
    touch-action: manipulation;
}

// Fix for keyboard overlay issues
ion-modal,
ion-popover {
    --backdrop-opacity: 0.4;
}

/**
 * Enhanced Ripple Effects
 * -----------------------------------------------------
 * Improved ripple effects for better user interaction
 */

// Enhanced ripple effect for better visibility
ion-ripple-effect {
    --ripple-color: currentColor;
    opacity: 0.2;
    transition: opacity 0.3s ease;
}

// Interactive elements with ripple effects
.interactive-element,
.add-shipping-container,
.shipping-tab-item,
.action-button,
.action-icons ion-icon {
    position: relative;
    overflow: hidden;
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;

    // Ensure ripple effect is visible
    ion-ripple-effect {
        z-index: 1;
        pointer-events: none;
    }
}

// Enhanced touch feedback
.interactive-element:active,
.add-shipping-container:active,
.shipping-tab-item:active,
.action-button:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
}

// Smooth transitions for all interactive elements
.interactive-element,
.add-shipping-container,
.shipping-tab-item,
.action-button,
.action-icons ion-icon {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

// Custom ripple colors for different elements
.primary-ripple ion-ripple-effect {
    --ripple-color: rgba(255, 234, 0, 0.4);
}

.secondary-ripple ion-ripple-effect {
    --ripple-color: rgba(255, 255, 255, 0.3);
}

.dark-ripple ion-ripple-effect {
    --ripple-color: rgba(0, 0, 0, 0.2);
}