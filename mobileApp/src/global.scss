/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;900&&family=Oswald:wght@200..700&family=Pacifico&display=swap");
@import "theme/variables.scss";
@import "theme/common.scss";
@import "theme/driver-dashboard.scss";
@import "theme/driver-register.scss";

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
/* @import "@ionic/angular/css/palettes/dark.class.css"; */

.loading-container {
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    position: fixed;
    z-index: 999999999;
    top: 0;

    .loader {
        margin: 0px auto;
    }

    svg {
        border-radius: 50%;
    }
}


/**
 * Mobile App Specific Styles
 * -----------------------------------------------------
 * Ensures proper mobile behavior and keyboard handling
 */

// Ensure proper mobile viewport
html,
body {
    height: 100vh;
    width: 100%;
    -webkit-overflow-scrolling: touch;
}

// Ensure ion-app takes full height
ion-app {
    height: 100vh;
}

// Fix for iOS keyboard issues
ion-content {
    --keyboard-offset: 0px;
}

// Prevent zoom on input focus (iOS)
input,
textarea,
select {
    font-size: 16px !important;
    -webkit-user-select: text;
    user-select: text;
}

// Smooth scrolling for mobile
* {
    -webkit-overflow-scrolling: touch;
}

// Fix for safe area on newer devices
.safe-area-top {
    padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
}

// Ensure proper touch behavior
.ion-page {
    touch-action: manipulation;
}

// Fix for keyboard overlay issues
ion-modal,
ion-popover {
    --backdrop-opacity: 0.4;
}

/**
 * Enhanced Ripple Effects
 * -----------------------------------------------------
 * Improved ripple effects for better user interaction
 */

// Enhanced ripple effect for better visibility
ion-ripple-effect {
    --ripple-color: currentColor;
    opacity: 0.2;
    transition: opacity 0.3s ease;
}

// Interactive elements with ripple effects
.interactive-element,
.add-shipping-container,
.shipping-tab-item,
.action-button,
.action-icons ion-icon {
    position: relative;
    overflow: hidden;
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;

    // Ensure ripple effect is visible
    ion-ripple-effect {
        z-index: 1;
        pointer-events: none;
    }
}

// Enhanced touch feedback
.interactive-element:active,
.add-shipping-container:active,
.shipping-tab-item:active,
.action-button:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
}

// Smooth transitions for all interactive elements
.interactive-element,
.add-shipping-container,
.shipping-tab-item,
.action-button,
.action-icons ion-icon {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

// Custom ripple colors for different elements
.primary-ripple ion-ripple-effect {
    --ripple-color: rgba(255, 234, 0, 0.4);
}

.secondary-ripple ion-ripple-effect {
    --ripple-color: rgba(255, 255, 255, 0.3);
}

.dark-ripple ion-ripple-effect {
    --ripple-color: rgba(0, 0, 0, 0.2);
}

/**
 * Custom Alert Styles
 * -----------------------------------------------------
 * Styled to match the app's design system with yellow accent
 */

.custom-styled-alert {
    --backdrop-opacity: 0.4;

    .alert-wrapper {
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        max-width: 320px;
        margin: 0 auto;
        background: white;
    }

    .alert-head {
        padding: 30px 30px 0;
        text-align: center;

        h2 {
            font-size: 22px;
            font-weight: 600;
            color: var(--ion-color-dark, #222428);
            margin: 0;
            font-family: var(--ion-font-family, 'Montserrat', sans-serif);
            position: relative;

            // Add yellow icon before header
            &::before {
                content: '';
                display: block;
                width: 80px;
                height: 80px;
                background: var(--ion-color-site-main-heading, #f6be00);
                border-radius: 50%;
                margin: 0 auto 20px;
                position: relative;
                box-shadow: 0 4px 15px rgba(246, 190, 0, 0.3);
            }

            // Add logout icon
            &::after {
                content: '🚪';
                position: absolute;
                top: -60px;
                left: 50%;
                transform: translateX(-50%);
                font-size: 30px;
                color: white;
                z-index: 1;
            }
        }
    }

    .alert-message {
        padding: 0 30px 20px;
        text-align: center;

        div {
            font-size: 16px;
            color: var(--ion-color-medium, #92949c);
            line-height: 1.4;
            font-family: var(--ion-font-family, 'Montserrat', sans-serif);
            margin: 0;
        }
    }

    .alert-button-group {
        padding: 0 30px 30px;
        display: flex;
        flex-direction: column;
        gap: 12px;

        .alert-button {
            padding: 16px 24px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            font-family: var(--ion-font-family, 'Montserrat', sans-serif);
            margin: 0;

            &:active {
                transform: scale(0.98);
            }
        }

        .alert-confirm-button {
            background: var(--ion-color-dark, #222428);
            color: white;
            order: 1;

            &:hover {
                background: var(--ion-color-dark-shade, #1e2023);
                box-shadow: 0 4px 12px rgba(34, 36, 40, 0.3);
            }
        }

        .alert-cancel-button {
            background: var(--ion-color-light, #f4f5f8);
            color: var(--ion-color-dark, #222428);
            border: 2px solid var(--ion-color-step-200, #cccccc) !important;
            order: 2;

            &:hover {
                background: var(--ion-color-step-100, #e6e6e6);
                border-color: var(--ion-color-step-300, #b3b3b3) !important;
            }
        }
    }
}

// Responsive design for alerts
@media (max-width: 480px) {
    .custom-styled-alert {
        .alert-wrapper {
            margin: 20px;
            max-width: calc(100vw - 40px);
        }

        .alert-head {
            padding: 20px 20px 0;

            h2 {
                font-size: 20px;

                &::before {
                    width: 70px;
                    height: 70px;
                }

                &::after {
                    top: -50px;
                    font-size: 25px;
                }
            }
        }

        .alert-message {
            padding: 0 20px 15px;

            div {
                font-size: 15px;
            }
        }

        .alert-button-group {
            padding: 0 20px 20px;
        }
    }
}