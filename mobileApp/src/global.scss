/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;900&&family=Oswald:wght@200..700&family=Pacifico&display=swap");
@import "theme/variables.scss";
@import "theme/common.scss";
@import "theme/driver-dashboard.scss";
@import "theme/driver-register.scss";

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
/* @import "@ionic/angular/css/palettes/dark.class.css"; */

.loading-container {
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    position: fixed;
    z-index: 999999999;
    top: 0;

    .loader {
        margin: 0px auto;
    }

    svg {
        border-radius: 50%;
    }
}


/**
 * Mobile App Specific Styles
 * -----------------------------------------------------
 * Ensures proper mobile behavior and keyboard handling
 */

// Ensure proper mobile viewport
html,
body {
    height: 100vh;
    width: 100%;
    -webkit-overflow-scrolling: touch;
}

// Ensure ion-app takes full height
ion-app {
    height: 100vh;
}

// Fix for iOS keyboard issues
ion-content {
    --keyboard-offset: 0px;
}

// Prevent zoom on input focus (iOS)
input,
textarea,
select {
    font-size: 16px !important;
    -webkit-user-select: text;
    user-select: text;
}

// Smooth scrolling for mobile
* {
    -webkit-overflow-scrolling: touch;
}

// Fix for safe area on newer devices
.safe-area-top {
    padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
}

// Ensure proper touch behavior
.ion-page {
    touch-action: manipulation;
}

// Fix for keyboard overlay issues
ion-modal,
ion-popover {
    --backdrop-opacity: 0.4;
}

/**
 * Enhanced Ripple Effects
 * -----------------------------------------------------
 * Improved ripple effects for better user interaction
 */

// Enhanced ripple effect for better visibility
ion-ripple-effect {
    --ripple-color: currentColor;
    opacity: 0.2;
    transition: opacity 0.3s ease;
}

// Interactive elements with ripple effects
.interactive-element,
.add-shipping-container,
.shipping-tab-item,
.action-button,
.action-icons ion-icon {
    position: relative;
    overflow: hidden;
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;

    // Ensure ripple effect is visible
    ion-ripple-effect {
        z-index: 1;
        pointer-events: none;
    }
}

// Enhanced touch feedback
.interactive-element:active,
.add-shipping-container:active,
.shipping-tab-item:active,
.action-button:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
}

// Smooth transitions for all interactive elements
.interactive-element,
.add-shipping-container,
.shipping-tab-item,
.action-button,
.action-icons ion-icon {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

// Custom ripple colors for different elements
.primary-ripple ion-ripple-effect {
    --ripple-color: rgba(255, 234, 0, 0.4);
}

.secondary-ripple ion-ripple-effect {
    --ripple-color: rgba(255, 255, 255, 0.3);
}

.dark-ripple ion-ripple-effect {
    --ripple-color: rgba(0, 0, 0, 0.2);
}

/**
 * Custom Logout Action Sheet Styles
 * -----------------------------------------------------
 * Styled to match the uploaded design exactly
 * Clean white card with yellow accent and proper button layout
 */

.custom-logout-action-sheet {
    --backdrop-opacity: 0.4;

    .action-sheet-wrapper {
        border-radius: 20px 20px 0 0;
        background: white;
        box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.15);
        margin: 0;
        padding: 0;
        max-width: 400px;
        margin: 0 auto;
    }

    .action-sheet-container {
        padding: 40px 30px 0;
        text-align: center;
        position: relative;
    }

    .action-sheet-title {
        font-size: 24px !important;
        font-weight: 600 !important;
        color: #1a1a1a !important;
        margin: 30px 0 8px 0 !important;
        font-family: var(--ion-font-family, 'Montserrat', sans-serif) !important;
        text-align: center !important;
        line-height: 1.2 !important;
    }

    .action-sheet-sub-title {
        font-size: 14px !important;
        color: #666666 !important;
        margin: 0 0 40px 0 !important;
        line-height: 1.4 !important;
        font-family: var(--ion-font-family, 'Montserrat', sans-serif) !important;
        text-align: center !important;
        font-weight: 400 !important;
    }

    // Yellow circle with icon
    .action-sheet-container::before {
        content: '';
        display: block;
        width: 80px;
        height: 80px;
        background: var(--ion-color-site-main-heading, #f6be00);
        border-radius: 50%;
        margin: 0 auto 0;
        position: relative;
        box-shadow: 0 4px 20px rgba(246, 190, 0, 0.25);
    }

    // Checkmark icon in yellow circle
    .action-sheet-container::after {
        content: '✓';
        position: absolute;
        top: 60px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 32px;
        color: white;
        z-index: 1;
        font-weight: bold;
    }

    .action-sheet-button-container {
        display: flex;
        gap: 12px;
        padding: 0 30px 30px;
        flex-direction: row;
    }

    .action-sheet-button {
        padding: 16px 20px !important;
        border-radius: 25px !important;
        font-size: 16px !important;
        font-weight: 600 !important;
        border: none !important;
        margin: 0 !important;
        width: 50% !important;
        flex: none !important;
        transition: all 0.2s ease !important;
        font-family: var(--ion-font-family, 'Montserrat', sans-serif) !important;
        min-height: 50px !important;

        &:active {
            transform: scale(0.98);
        }
    }

    .cancel-button {
        background: #f5f5f5 !important;
        color: #333333 !important;
        border: 1px solid #e0e0e0 !important;

        &:hover {
            background: #eeeeee !important;
            border-color: #d0d0d0 !important;
        }
    }

    .logout-button {
        background: #1a1a1a !important;
        color: white !important;

        &:hover {
            background: #333333 !important;
            box-shadow: 0 4px 12px rgba(26, 26, 26, 0.3) !important;
        }
    }
}

// Responsive design for action sheet
@media (max-width: 480px) {
    .custom-logout-action-sheet {
        .action-sheet-wrapper {
            margin: 0 10px;
            max-width: calc(100vw - 20px);
        }

        .action-sheet-container {
            padding: 30px 20px 0;

            &::before {
                width: 70px;
                height: 70px;
            }

            &::after {
                top: 50px;
                font-size: 28px;
            }
        }

        .action-sheet-title {
            font-size: 22px !important;
            margin: 25px 0 8px 0 !important;
        }

        .action-sheet-sub-title {
            font-size: 13px !important;
            margin: 0 0 35px 0 !important;
        }

        .action-sheet-button-container {
            padding: 0 20px 25px;
            gap: 10px;
        }

        .action-sheet-button {
            padding: 14px 16px !important;
            font-size: 15px !important;
            min-height: 46px !important;
        }
    }
}

