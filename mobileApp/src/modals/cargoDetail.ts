export class CargoDetails {
  shipment: string | null = null;
  quantity: number | null = null;
  description: string | null = null;
  cargoType: string | null = null;
  weightType: string | null = null;
  weightInPounds: number | null = null;
  rateType: string | null = null;
  weight: number | null = null;  // ✅ changed to number
  length: number | null = null;  // ✅ changed to number
  volume: number | null = null;
  width: number | null = null;   // ✅ changed to number
  height: number | null = null;  // ✅ changed to number

  static fromResponse(data: any): CargoDetails {
    const detail = new CargoDetails();
    detail.shipment = data.shipment ?? null;
    detail.quantity = data.quantity ?? null;
    detail.description = data.description ?? null;
    detail.cargoType = data.cargoType ?? null;
    detail.weightType = data.weightType ?? null;
    detail.weightInPounds = data.weightInPounds ?? null;
    detail.rateType = data.rateType ?? null;
    detail.weight = data.weight ?? null;
    detail.length = data.length ?? null;
    detail.volume = data.volume ?? null;
    detail.width = data.width ?? null;
    detail.height = data.height ?? null;
    return detail;
  }
}

export class SpecialRequestStates {
  id: string | null = null;
  oversize: boolean = true;
  rushRequest: boolean = false;
  enclosed: boolean = false;
  fragile: boolean = true;
  perishable: boolean = true;
  dangerousGoods: boolean = false;
  //  oversizeComment: string = '';

  static fromResponse(data: any): SpecialRequestStates {
    const request = new SpecialRequestStates();
    request.id = data.id ?? data.shipment ?? null; // Support both field names for backward compatibility
    request.oversize = data.oversize ?? false;
    request.rushRequest = data.rushRequest ?? false;
    request.enclosed = data.enclosed ?? false;
    request.fragile = data.fragile ?? false;
    request.perishable = data.perishable ?? false;
    request.dangerousGoods = data.dangerousGoods ?? false;

    //  request.oversizeComment = data.oversizeComment ?? '';

    return request;
  }
}
