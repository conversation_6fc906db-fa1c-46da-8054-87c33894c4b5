export interface UserProfileResponse {
  status: number;
  message: string;
  data: UserProfileData;
}

export interface UserProfileData {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  profileImageUrl?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export class UserProfileModel {
  firstName: string = '';
  lastName: string = '';
  email: string = '';
  phoneNumber: string = '';
  profileImageUrl?: string = '';

  constructor(data?: Partial<UserProfileData>) {
    if (data) {
      this.firstName = data.firstName || '';
      this.lastName = data.lastName || '';
      this.email = data.email || '';
      this.phoneNumber = data.phoneNumber || '';
      this.profileImageUrl = data.profileImageUrl || '';
    }
  }

  // Convert to API format for PUT request
  toApiFormat(): any {
    return {
      firstName: this.firstName.trim(),
      lastName: this.lastName.trim(),
      email: this.email.trim(),
      phoneNumber: this.phoneNumber.trim(),
      profileImageUrl: this.profileImageUrl
    };
  }

  // Validation method
  isValid(form: any): boolean {
    let valid = true;

    if (!this.firstName || this.firstName.trim() === '') {
      form.controls.firstName?.setErrors({ required: true });
      valid = false;
    }

    if (!this.lastName || this.lastName.trim() === '') {
      form.controls.lastName?.setErrors({ required: true });
      valid = false;
    }

    if (!this.email || this.email.trim() === '') {
      form.controls.email?.setErrors({ required: true });
      valid = false;
    }

    if (!this.phoneNumber || this.phoneNumber.trim() === '') {
      form.controls.phoneNumber?.setErrors({ required: true });
      valid = false;
    }

    return valid;
  }

  // Static method to create from API response
  static fromApiResponse(data: UserProfileData): UserProfileModel {
    return new UserProfileModel(data);
  }
}
