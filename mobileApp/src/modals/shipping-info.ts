export class PickupAddressDetail {
  address: string | null = null;
  city: string | null = null;
  state: string | null = null;
  country: string | null = null;
  pin: string | null = null;
  latitude: string | null = null;
  longitude: string | null = null;

  static fromResponse(data: any): PickupAddressDetail {
    const addr = new PickupAddressDetail();
    addr.address = data.address ?? null;
    addr.city = data.city ?? null;
    addr.state = data.state ?? null;
    addr.country = data.country ?? null;
    addr.pin = data.pin ?? null;
    addr.latitude = data.latitude ?? null;
    addr.longitude = data.longitude ?? null;
    return addr;
  }
}

export class DeliveryAddressDetail {
  address: string | null = null;
  city: string | null = null;
  state: string | null = null;
  country: string | null = null;
  pin: string | null = null;
  latitude: string | null = null;
  longitude: string | null = null;

  static fromResponse(data: any): DeliveryAddressDetail {
    const addr = new DeliveryAddressDetail();
    addr.address = data.address ?? null;
    addr.city = data.city ?? null;
    addr.state = data.state ?? null;
    addr.country = data.country ?? null;
    addr.pin = data.pin ?? null;
    addr.latitude = data.latitude ?? null;
    addr.longitude = data.longitude ?? null;
    return addr;
  }
}

export class ShippingBasicInfo {
  refID: string | null = null;
  customer: string | null = null;
  contactPersonEmail: string | null = null;
  contactPersonName: string | null = null;
  contactPersonPhone: string | null = null;
  contactPersonCountryCode: string | null = "+1-CA";
  barcode: string | null = null;
  driver: string | null = null;
  status: string | null = null;
  shipmentType: string | null = null;
  paymentType: string | null = null;
  paymentStatus: string | null = null;
  etd: string | null = null;
  isSecured: boolean = false;
  summary: string | null = null;
  pickupContactPersonName: string | null = null;
  pickupCompanyName: string | null = null;
  pickupContactPersonPhone: string | null = null;
  pickupContactCountryCode: string | null = "+1-CA";
  pickupAddressDetail: PickupAddressDetail = new PickupAddressDetail();
  deliveryCompanyName: string | null = null;
  deliveryContactPersonName: string | null = null;
  deliveryContactPersonPhone: string | null = null;
  deliveryContactCountryCode: string | null = "+1-CA";
  deliveryAddressDetail: DeliveryAddressDetail = new DeliveryAddressDetail();

  static fromResponse(data: any): ShippingBasicInfo {
    const info = new ShippingBasicInfo();
    info.refID = data.refID ?? null;
    info.customer = data.customer ?? null;
    info.contactPersonEmail = data.contactPersonEmail ?? null;
    info.contactPersonName = data.contactPersonName ?? null;
    info.contactPersonPhone = data.contactPersonPhone ?? null;
    info.contactPersonCountryCode = data.contactPersonCountryCode ?? null;
    info.barcode = data.barcode ?? null;
    info.driver = data.driver ?? null;
    info.status = data.status ?? null;
    info.shipmentType = data.shipmentType ?? null;
    info.paymentType = data.paymentType ?? null;
    info.paymentStatus = data.paymentStatus ?? null;
    info.etd = data.etd ?? null;
    info.isSecured = data.isSecured ?? false;
    info.summary = data.summary ?? null;
    info.pickupContactPersonName = data.pickupContactPersonName ?? null;
    info.pickupCompanyName = data.pickupCompanyName ?? null;
    info.pickupContactPersonPhone = data.pickupContactPersonPhone ?? null;
    info.pickupContactCountryCode = data.pickupContactCountryCode ?? null;
    info.deliveryCompanyName = data.deliveryCompanyName ?? null;
    info.deliveryContactPersonName = data.deliveryContactPersonName ?? null;
    info.deliveryContactPersonPhone = data.deliveryContactPersonPhone ?? null;
    info.deliveryContactCountryCode = data.deliveryContactCountryCode ?? null;

    if (data.pickupAddressDetail) {
      info.pickupAddressDetail = PickupAddressDetail.fromResponse(data.pickupAddressDetail);
    }
    if (data.deliveryAddressDetail) {
      info.deliveryAddressDetail = DeliveryAddressDetail.fromResponse(data.deliveryAddressDetail);
    }

    return info;
  }

}

export interface ShipmentData {
  id: string;
  createdOn: string;
  customerUserDetail: CustomerUserDetail;
  refID: string;
  etd?: string;
  shipmentType: string;
  paymentType: string;
  status: string;
  paymentStatus: string;
  rateSheetDetail?: RateSheetDetail;
  grandTotal: number;
  pickupAddressDetail: AddressDetail;
  deliveryAddressDetail?: AddressDetail;
  step: number;
  totalCount: number;
  isCargoAdded?: boolean;
}

export interface CustomerUserDetail {
  fullName: string;
  twoFactorEnabled: boolean;
  isTwoFactorSetUp: boolean;
  totalCount: number;
}

export interface RateSheetDetail {
  name: string;
  totalCount: number;
}

export interface AddressDetail {
  city: string;
  totalCount: number;
}

// UI Model for Driver Shipping Component
export interface DriverShipmentItem {
  id: string;
  refId: string;
  paymentType: string;
  from: string;
  to: string;
  etd: string;
  status: string;
  originalStatus?: string; // Store original API status for button logic
  customerName: string;
  grandTotal: number;
  shipmentType: string;
  step: number;
  isCargoAdded?: boolean;
}

