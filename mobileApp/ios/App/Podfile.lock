PODS:
  - Capacitor (7.2.0):
    - CapacitorCordova
  - CapacitorCamera (7.0.1):
    - Capacitor
  - CapacitorCordova (7.2.0)
  - CapacitorGeolocation (7.1.2):
    - Capacitor
    - IONGeolocationLib (~> 1.0)
  - CapacitorSplashScreen (7.0.1):
    - Capacitor
  - CapacitorStatusBar (7.0.1):
    - Capacitor
  - CapacitorToast (7.0.1):
    - Capacitor
  - CapawesomeCapacitorBadge (7.0.1):
    - Capacitor
  - IONGeolocationLib (1.0.0)
  - JcesarmobileSslSkip (0.4.0):
    - Capacitor
  - PantristCapacitorDatePicker (7.0.0):
    - Capacitor

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorCamera (from `../../node_modules/@capacitor/camera`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorGeolocation (from `../../node_modules/@capacitor/geolocation`)"
  - "CapacitorSplashScreen (from `../../node_modules/@capacitor/splash-screen`)"
  - "CapacitorStatusBar (from `../../node_modules/@capacitor/status-bar`)"
  - "CapacitorToast (from `../../node_modules/@capacitor/toast`)"
  - "CapawesomeCapacitorBadge (from `../../node_modules/@capawesome/capacitor-badge`)"
  - "JcesarmobileSslSkip (from `../../node_modules/@jcesarmobile/ssl-skip`)"
  - "PantristCapacitorDatePicker (from `../../node_modules/@pantrist/capacitor-date-picker`)"

SPEC REPOS:
  trunk:
    - IONGeolocationLib

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorCamera:
    :path: "../../node_modules/@capacitor/camera"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorGeolocation:
    :path: "../../node_modules/@capacitor/geolocation"
  CapacitorSplashScreen:
    :path: "../../node_modules/@capacitor/splash-screen"
  CapacitorStatusBar:
    :path: "../../node_modules/@capacitor/status-bar"
  CapacitorToast:
    :path: "../../node_modules/@capacitor/toast"
  CapawesomeCapacitorBadge:
    :path: "../../node_modules/@capawesome/capacitor-badge"
  JcesarmobileSslSkip:
    :path: "../../node_modules/@jcesarmobile/ssl-skip"
  PantristCapacitorDatePicker:
    :path: "../../node_modules/@pantrist/capacitor-date-picker"

SPEC CHECKSUMS:
  Capacitor: 03bc7cbdde6a629a8b910a9d7d78c3cc7ed09ea7
  CapacitorCamera: 10b70651024761a584dd8d8c68d205561346ef4e
  CapacitorCordova: 5967b9ba03915ef1d585469d6e31f31dc49be96f
  CapacitorGeolocation: 05f80d1fab1bbf0391619b4145b0cb7ae89ce8e0
  CapacitorSplashScreen: e074262ca721df1ddce636ef7ea976a435b20239
  CapacitorStatusBar: 53b0a6656257653773e70b7f916a698cb0fd0b7d
  CapacitorToast: 6d279d106b1d504db9cd2a67c59e8594be11aa8c
  CapawesomeCapacitorBadge: da0a306ac3d1ae31219d8b63574b345ee796fff8
  IONGeolocationLib: 81f33f88d025846946de2cf63b0c7628e7c6bc9d
  JcesarmobileSslSkip: 21e77212233d70e3d6c2ccd85e01d490532f9610
  PantristCapacitorDatePicker: 9f9eade86321dc6edc0db9b3c544efb448bdfad3

PODFILE CHECKSUM: 4dcf269527aa5274f9596134eb4b7db357dc01dc

COCOAPODS: 1.16.2
